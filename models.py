"""
数据模型定义
包含所有API请求/响应模型和业务数据模型
"""

import datetime
from typing import Optional, List
from enum import Enum
from pydantic import BaseModel


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class RunTaskReqArg(BaseModel):
    """运行任务请求参数"""
    taskId: str
    task: str
    extSysMsg: str = ""
    adbDevice: str = ""


class RunTaskReq(BaseModel):
    """运行任务请求"""
    v: str = "1.0.0"
    arg: RunTaskReqArg


class TaskResData(BaseModel):
    """任务响应数据"""
    taskId: str
    status: TaskStatus
    message: str


class TaskRes(BaseModel):
    """任务响应"""
    code: int
    msg: str
    data: TaskResData


class TaskInfo(BaseModel):
    """任务信息"""
    taskId: str
    task: str
    extSysMsg: str
    adbDevice: str
    status: TaskStatus
    created_at: datetime.datetime
    completed_at: Optional[datetime.datetime] = None
    result: Optional[str] = None
    error: Optional[str] = None


class IntentArg(BaseModel):
    """意图参数"""
    drinkName: str
    shop: str   = ""
    options: str = ""


class Intent(BaseModel):
    """意图信息"""
    intentType: str
    site: str
    action: str
    toWhom: str
    response: str
    arg: IntentArg


class RunIntentTaskReqArg(BaseModel):
    """运行意图任务请求参数"""
    taskId: str
    intent: Intent
    adbDevice: str = ""



class RunIntentTaskReq(BaseModel):
    """运行意图任务请求"""
    v: str = "1.0.0"
    arg: RunIntentTaskReqArg

    


class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str
    timestamp: str
    total_tasks: int
    running_tasks: int
    task_status_counts: dict


class RootResponse(BaseModel):
    """根路径响应"""
    message: str
    version: str
    status: str
    total_tasks: int
    running_tasks: int
