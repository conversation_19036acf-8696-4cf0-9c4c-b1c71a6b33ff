# Browser Use API 高级日志系统优化说明

## 优化概述

本次优化实现了企业级的日志管理系统，支持按天滚动、分级日志、日志分析和管理工具，大幅提升了API的可观测性、调试能力和运维效率。

## 主要优化内容

### 1. 高级日志系统

#### 1.1 按天滚动日志
- **自动滚动**：每天午夜自动创建新的日志文件
- **文件命名**：使用日期后缀格式 (如: browser_use_api.log.2024-01-20)
- **保留策略**：默认保留30天的日志文件，可配置
- **编码支持**：UTF-8编码，支持中文日志

#### 1.2 分级日志文件
- **主日志** (`browser_use_api.log`)：记录所有级别的日志
- **错误日志** (`error.log`)：只记录ERROR及以上级别
- **请求日志** (`requests.log`)：专门记录HTTP请求
- **业务日志** (`business.log`)：专门记录业务逻辑执行

#### 1.3 专用日志记录器
- `browser_use_api`：主要业务日志
- `browser_use_api.requests`：HTTP请求日志
- `browser_use_api.business`：业务逻辑执行日志

#### 1.4 彩色控制台输出
- **颜色编码**：不同日志级别使用不同颜色显示
- **可配置**：可通过环境变量控制是否启用颜色
- **兼容性**：自动检测终端支持情况

### 2. 请求日志中间件

#### 2.1 HTTP请求监控
- 记录所有HTTP请求的开始和结束
- 包含请求方法、URL、客户端IP
- 记录响应状态码和处理时间
- 自动计算请求处理耗时

#### 2.2 CORS支持
- 添加了CORS中间件，支持跨域请求
- 允许所有来源、方法和头部

### 3. 业务逻辑日志优化

#### 3.1 任务执行日志
- 详细记录任务执行的每个阶段
- LLM配置过程日志
- 浏览器会话配置日志
- Agent创建和执行日志
- 任务完成/失败/取消的详细信息
- 执行时间统计

#### 3.2 错误处理日志
- 详细的异常信息记录
- 任务取消和失败的原因追踪
- 资源清理过程日志

### 4. API端点优化

#### 4.1 创建任务端点 (`POST /run-task`)
- 记录任务创建过程
- 任务ID生成/使用日志
- 重复任务检查
- 任务信息存储日志
- 后台任务启动日志

#### 4.2 查询任务端点 (`GET /tasks/{taskId}`)
- 任务查询日志
- 任务不存在的警告日志
- 当前任务状态记录

#### 4.3 任务列表端点 (`GET /tasks`)
- 任务列表查询日志
- 任务数量统计
- 各状态任务分布统计

#### 4.4 取消任务端点 (`DELETE /tasks/{taskId}`)
- 任务取消操作日志
- 任务状态检查日志
- 运行中任务的取消过程日志

#### 4.5 健康检查端点 (`GET /health`)
- 新增专用健康检查端点
- 返回详细的系统状态信息
- 包含任务统计和状态分布

### 5. 根端点增强 (`GET /`)
- 增强了根端点返回信息
- 包含API版本、状态、任务统计等信息

## 日志示例

### 请求日志示例
```
2024-01-20 10:30:15,123 - browser_use_api.requests - INFO - Request started - Method: POST | URL: http://localhost:8080/run-task | Client IP: 127.0.0.1
2024-01-20 10:30:15,456 - browser_use_api.requests - INFO - Request completed - Method: POST | URL: http://localhost:8080/run-task | Status: 200 | Process time: 0.333s
```

### 业务逻辑日志示例
```
2024-01-20 10:30:15,124 - browser_use_api - INFO - POST /run-task - Creating new browser task
2024-01-20 10:30:15,125 - browser_use_api - INFO - Generated new taskId: 12345678-1234-5678-9abc-123456789abc
2024-01-20 10:30:15,126 - browser_use_api.business - INFO - Task 12345678-1234-5678-9abc-123456789abc - Starting browser task execution
2024-01-20 10:30:15,127 - browser_use_api.business - INFO - Task 12345678-1234-5678-9abc-123456789abc - Configuring LLM (DeepSeek)
```

### 2. 日志管理工具

#### 2.1 专用管理脚本 (`log_manager.py`)
- **列出日志文件**：查看所有日志文件及大小
- **查看日志末尾**：实时查看最新日志
- **搜索日志内容**：支持正则表达式搜索
- **分析日志统计**：生成日志统计报告
- **清理旧日志**：自动清理过期日志文件
- **压缩日志**：压缩旧日志文件节省空间

#### 2.2 环境配置支持
- **配置文件**：`.env` 文件支持环境变量配置
- **灵活配置**：日志级别、保留天数、颜色等可配置
- **默认值**：提供合理的默认配置

### 3. 新增文件说明

#### 3.1 核心文件
- `log_config.py`：高级日志配置模块
- `log_manager.py`：日志管理工具脚本
- `.env.example`：环境配置示例文件

#### 3.2 日志文件结构
```
logs/
├── browser_use_api.log         # 主日志文件
├── browser_use_api.log.2024-01-19  # 昨天的日志
├── error.log                   # 错误日志
├── requests.log               # 请求日志
├── business.log               # 业务日志
└── *.log.gz                   # 压缩的历史日志
```

## 使用说明

### 1. 环境配置
```bash
# 复制配置文件
cp .env.example .env

# 编辑配置（可选）
# 修改日志级别、保留天数等配置
```

### 2. 启动API服务
```bash
python main.py
```

### 3. 日志管理
```bash
# 列出所有日志文件
python log_manager.py list

# 查看主日志最后50行
python log_manager.py tail --type main --lines 50

# 查看错误日志
python log_manager.py tail --type error

# 搜索包含"ERROR"的日志
python log_manager.py search "ERROR"

# 分析最近7天的日志统计
python log_manager.py analyze --days 7

# 清理30天前的日志（预览）
python log_manager.py clean --days 30

# 实际清理30天前的日志
python log_manager.py clean --days 30 --no-dry-run

# 压缩7天前的日志文件
python log_manager.py compress --days 7
```

### 4. 测试API
```bash
python test_api.py
```

### 5. 健康检查
```bash
curl http://localhost:8080/health
```

## 监控和调试

### 1. 实时日志监控
```bash
# 实时查看主日志
python log_manager.py tail --type main --lines 100

# 实时查看错误日志
python log_manager.py tail --type error

# 搜索特定任务的日志
python log_manager.py search "Task.*12345678"
```

### 2. 日志分析
```bash
# 生成日志统计报告
python log_manager.py analyze --days 30

# 搜索性能问题
python log_manager.py search "Process time.*[5-9]\.[0-9]"

# 查找失败的任务
python log_manager.py search "Task.*failed"
```

### 3. 日志维护
```bash
# 查看日志文件大小
python log_manager.py list

# 压缩旧日志节省空间
python log_manager.py compress --days 7

# 清理过期日志
python log_manager.py clean --days 30 --no-dry-run
```

## 配置选项

### 环境变量配置
```bash
# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# 日志保留天数
LOG_BACKUP_COUNT=30

# 控制台颜色 (true/false)
LOG_CONSOLE_COLORS=true

# 日志目录
LOG_DIR=logs
```

### 日志级别说明
- **DEBUG**：详细的调试信息
- **INFO**：一般信息（默认）
- **WARNING**：警告信息
- **ERROR**：错误信息
- **CRITICAL**：严重错误

## 性能优化

### 1. 日志性能
- **异步写入**：日志写入不阻塞主线程
- **缓冲输出**：减少磁盘I/O操作
- **分级存储**：不同级别日志分别存储

### 2. 存储优化
- **自动滚动**：防止单个文件过大
- **压缩存储**：历史日志自动压缩
- **定期清理**：自动清理过期日志

### 3. 查询优化
- **索引搜索**：支持正则表达式快速搜索
- **分类查看**：按类型查看特定日志
- **统计分析**：快速生成统计报告

## 注意事项

1. **磁盘空间**：定期清理和压缩日志文件
2. **日志级别**：生产环境建议使用INFO或WARNING级别
3. **敏感信息**：API密钥等敏感信息已自动隐藏
4. **权限管理**：确保日志目录有适当的读写权限
5. **监控告警**：建议配置日志监控和告警系统

## 故障排查

### 常见问题
1. **日志文件不存在**：检查日志目录权限和磁盘空间
2. **日志不滚动**：检查系统时间和时区设置
3. **搜索无结果**：检查正则表达式语法和大小写
4. **压缩失败**：检查磁盘空间和文件权限

### 调试技巧
```bash
# 检查日志配置
python -c "from log_config import setup_logging; setup_logging()"

# 测试日志功能
python log_config.py

# 验证日志轮转
ls -la logs/
```
