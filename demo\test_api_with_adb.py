#!/usr/bin/env python3
"""
API测试脚本 - 支持ADB设备
用于测试重构后的API是否正常工作，特别是ADB设备连接功能
"""

import requests
import json
import time
import sys
from typing import Optional

class BrowserUseAPITester:
    """Browser Use API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def test_health(self) -> bool:
        """测试健康检查端点"""
        print("=== 测试健康检查端点 ===")
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                data = response.json()
                print("✅ 健康检查通过")
                print(f"  状态: {data.get('status')}")
                print(f"  总任务数: {data.get('total_tasks', 0)}")
                print(f"  运行中任务数: {data.get('running_tasks', 0)}")
                return True
            else:
                print(f"❌ 健康检查失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {str(e)}")
            return False
    
    def create_task(self, task_description: str, ext_sys_msg: str = "", 
                   adb_device: str = "", task_id: str = "") -> Optional[str]:
        """创建任务"""
        print(f"=== 创建任务 ===")
        print(f"任务描述: {task_description}")
        if adb_device:
            print(f"ADB设备: {adb_device}")
        
        payload = {
            "v": "1.0.0",
            "arg": {
                "taskId": task_id,
                "task": task_description,
                "extSysMsg": ext_sys_msg,
                "adbDevice": adb_device
            }
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/aia/auto/run-task",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                task_id = data.get('data', {}).get('taskId')
                print(f"✅ 任务创建成功")
                print(f"  任务ID: {task_id}")
                print(f"  状态: {data.get('data', {}).get('status')}")
                print(f"  消息: {data.get('data', {}).get('message')}")
                return task_id
            else:
                print(f"❌ 任务创建失败: HTTP {response.status_code}")
                print(f"  响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 任务创建异常: {str(e)}")
            return None
    
    def get_task_status(self, task_id: str) -> Optional[dict]:
        """获取任务状态"""
        try:
            response = self.session.get(f"{self.base_url}/api/aia/auto/tasks/{task_id}")
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ 获取任务状态失败: HTTP {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ 获取任务状态异常: {str(e)}")
            return None
    
    def monitor_task(self, task_id: str, timeout: int = 300) -> bool:
        """监控任务执行"""
        print(f"=== 监控任务执行 ===")
        print(f"任务ID: {task_id}")
        print(f"超时时间: {timeout}秒")
        
        start_time = time.time()
        last_status = None
        
        while time.time() - start_time < timeout:
            task_info = self.get_task_status(task_id)
            if not task_info:
                time.sleep(5)
                continue
            
            status = task_info.get('status')
            if status != last_status:
                print(f"  状态更新: {status}")
                last_status = status
            
            if status in ['completed', 'failed', 'cancelled']:
                print(f"✅ 任务执行完成")
                print(f"  最终状态: {status}")
                if task_info.get('result'):
                    print(f"  执行结果: {task_info['result']}")
                if task_info.get('error'):
                    print(f"  错误信息: {task_info['error']}")
                return status == 'completed'
            
            time.sleep(5)
        
        print(f"❌ 任务监控超时")
        return False
    
    def list_tasks(self) -> bool:
        """列出所有任务"""
        print("=== 任务列表 ===")
        try:
            response = self.session.get(f"{self.base_url}/api/aia/auto/tasks")
            if response.status_code == 200:
                tasks = response.json()
                print(f"✅ 获取到 {len(tasks)} 个任务")
                for i, task in enumerate(tasks, 1):
                    print(f"  任务 {i}:")
                    print(f"    ID: {task.get('taskId')}")
                    print(f"    状态: {task.get('status')}")
                    print(f"    描述: {task.get('task', '')[:50]}...")
                    if task.get('adbDevice'):
                        print(f"    ADB设备: {task.get('adbDevice')}")
                return True
            else:
                print(f"❌ 获取任务列表失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取任务列表异常: {str(e)}")
            return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python test_api_with_adb.py <command> [options]")
        print()
        print("命令:")
        print("  health                    - 测试健康检查")
        print("  create <task> [adb_device] - 创建任务")
        print("  monitor <task_id>         - 监控任务")
        print("  list                      - 列出所有任务")
        print()
        print("示例:")
        print("  python test_api_with_adb.py health")
        print("  python test_api_with_adb.py create '打开淘宝搜索商品'")
        print("  python test_api_with_adb.py create '打开淘宝搜索商品' ***********:4631")
        print("  python test_api_with_adb.py monitor task-123")
        print("  python test_api_with_adb.py list")
        sys.exit(1)
    
    command = sys.argv[1]
    tester = BrowserUseAPITester()
    
    if command == "health":
        success = tester.test_health()
        sys.exit(0 if success else 1)
    
    elif command == "create":
        if len(sys.argv) < 3:
            print("错误: 需要提供任务描述")
            sys.exit(1)
        
        task_description = sys.argv[2]
        adb_device = sys.argv[3] if len(sys.argv) > 3 else ""
        
        task_id = tester.create_task(task_description, adb_device=adb_device)
        if task_id:
            print(f"\n任务创建成功，ID: {task_id}")
            print(f"可以使用以下命令监控任务:")
            print(f"  python test_api_with_adb.py monitor {task_id}")
        sys.exit(0 if task_id else 1)
    
    elif command == "monitor":
        if len(sys.argv) < 3:
            print("错误: 需要提供任务ID")
            sys.exit(1)
        
        task_id = sys.argv[2]
        success = tester.monitor_task(task_id)
        sys.exit(0 if success else 1)
    
    elif command == "list":
        success = tester.list_tasks()
        sys.exit(0 if success else 1)
    
    else:
        print(f"错误: 未知命令 '{command}'")
        sys.exit(1)

if __name__ == "__main__":
    main()
