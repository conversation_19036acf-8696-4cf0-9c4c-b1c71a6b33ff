# Browser Use API - 高级日志系统

## 🚀 快速开始

### 1. 设置环境
```bash
python start.py setup
```

### 2. 启动API服务
```bash
python start.py start
```

### 3. 测试API
```bash
python start.py test
```

### 4. 查看系统状态
```bash
python start.py status
```

## 📊 日志功能

### 日志文件结构
```
logs/
├── browser_use_api.log         # 主日志 (所有级别)
├── error.log                   # 错误日志 (ERROR+)
├── requests.log               # HTTP请求日志
├── business.log               # 业务逻辑日志
├── browser_use_api.log.2024-01-19  # 历史日志
└── *.log.gz                   # 压缩的历史日志
```

### 日志特性
- ✅ **按天自动滚动** - 每天午夜自动创建新日志文件
- ✅ **分级存储** - 不同类型日志分别存储
- ✅ **彩色输出** - 控制台日志支持颜色显示
- ✅ **自动压缩** - 历史日志自动压缩节省空间
- ✅ **智能清理** - 自动清理过期日志文件
- ✅ **快速搜索** - 支持正则表达式搜索
- ✅ **统计分析** - 自动生成日志统计报告

## 🛠️ 日志管理

### 查看日志
```bash
# 查看主日志最后50行
python start.py logs

# 查看错误日志
python start.py logs --type error

# 查看更多行数
python start.py logs --lines 100
```

### 搜索日志
```bash
# 搜索错误信息
python log_manager.py search "ERROR"

# 搜索特定任务
python log_manager.py search "Task.*12345678"

# 在所有日志中搜索
python log_manager.py search "pattern" --type all
```

### 分析日志
```bash
# 分析最近7天的日志
python start.py analyze

# 分析最近30天的日志
python start.py analyze --days 30
```

### 清理日志
```bash
# 预览清理30天前的日志
python start.py clean

# 实际清理30天前的日志
python start.py clean --execute

# 压缩7天前的日志
python log_manager.py compress --days 7
```

## ⚙️ 配置选项

### 环境变量 (.env)
```bash
# 日志级别
LOG_LEVEL=INFO

# 日志保留天数
LOG_BACKUP_COUNT=30

# 控制台颜色
LOG_CONSOLE_COLORS=true

# 日志目录
LOG_DIR=logs
```

### 日志级别说明
- `DEBUG` - 详细调试信息
- `INFO` - 一般信息 (默认)
- `WARNING` - 警告信息
- `ERROR` - 错误信息
- `CRITICAL` - 严重错误

## 📈 监控示例

### 实时监控
```bash
# 实时查看主日志
python log_manager.py tail --type main --lines 100

# 监控错误日志
python log_manager.py tail --type error
```

### 性能分析
```bash
# 查找慢请求 (>5秒)
python log_manager.py search "Process time.*[5-9]\.[0-9]"

# 查找失败的任务
python log_manager.py search "Task.*failed"

# 统计请求数量
python log_manager.py search "POST /run-task" | wc -l
```

### 错误追踪
```bash
# 查看所有错误
python log_manager.py search "ERROR"

# 查看特定时间的错误
python log_manager.py search "2024-01-20.*ERROR"

# 分析错误趋势
python start.py analyze --days 30
```

## 🔧 高级用法

### 自定义日志配置
```python
from log_config import setup_logging

# 自定义配置
setup_logging(
    log_dir="custom_logs",
    log_level="DEBUG",
    backup_count=60,
    enable_console_colors=False
)
```

### 程序中使用日志
```python
from log_config import get_logger

# 获取日志记录器
logger = get_logger("my_module")
request_logger = get_logger("browser_use_api.requests")
business_logger = get_logger("browser_use_api.business")

# 记录日志
logger.info("这是一条信息")
logger.error("这是一条错误")
request_logger.info("HTTP请求日志")
business_logger.info("业务逻辑日志")
```

## 📋 常用命令速查

| 命令 | 说明 |
|------|------|
| `python start.py start` | 启动API服务器 |
| `python start.py test` | 测试API功能 |
| `python start.py status` | 查看系统状态 |
| `python start.py logs` | 查看主日志 |
| `python start.py analyze` | 分析日志统计 |
| `python log_manager.py list` | 列出所有日志文件 |
| `python log_manager.py search "ERROR"` | 搜索错误日志 |
| `python log_manager.py clean --execute` | 清理过期日志 |

## 🚨 故障排查

### 常见问题
1. **日志文件不生成** - 检查目录权限和磁盘空间
2. **日志不滚动** - 检查系统时间设置
3. **搜索无结果** - 检查正则表达式语法
4. **服务器无法启动** - 查看error.log文件

### 调试步骤
```bash
# 1. 检查系统状态
python start.py status

# 2. 查看错误日志
python start.py logs --type error

# 3. 分析日志统计
python start.py analyze

# 4. 测试日志功能
python log_config.py
```

## 📚 更多信息

- 详细配置说明：`OPTIMIZATION_README.md`
- 环境配置示例：`.env.example`
- 日志管理工具：`log_manager.py`
- API测试脚本：`test_api.py`

---

**提示**: 生产环境建议设置 `LOG_LEVEL=WARNING` 以减少日志量，并配置日志监控告警系统。
