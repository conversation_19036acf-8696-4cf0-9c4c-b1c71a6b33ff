"""
Browser Use API 主应用程序
重构后的主入口文件，采用模块化架构
"""

import time
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware

# 导入配置和模块
from config import app_config
from log_config import setup_logging, get_logger, log_system_info
from api_routes import router

# 设置高级日志系统
setup_logging(
    log_dir=app_config.log.log_dir,
    log_level=app_config.log.log_level,
    backup_count=app_config.log.backup_count,
    enable_console_colors=app_config.log.enable_console_colors
)

# 记录系统信息
log_system_info()

# 创建专用的日志记录器
logger = get_logger("browser_use_api")
request_logger = get_logger("browser_use_api.requests")

# 创建FastAPI应用
app = FastAPI(
    title=app_config.server.title,
    version=app_config.server.version
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录HTTP请求的中间件"""
    start_time = time.time()

    # 记录请求开始
    request_logger.info(
        f"Request started - Method: {request.method} | "
        f"URL: {request.url} | "
        f"Client IP: {request.client.host if request.client else 'unknown'}"
    )

    # 处理请求
    response = await call_next(request)

    # 计算处理时间
    process_time = time.time() - start_time

    # 记录请求完成
    request_logger.info(
        f"Request completed - Method: {request.method} | "
        f"URL: {request.url} | "
        f"Status: {response.status_code} | "
        f"Process time: {process_time:.3f}s"
    )

    return response


# 包含所有API路由
app.include_router(router)


if __name__ == "__main__":
    """启动应用程序"""
    import uvicorn

    logger.info("Starting Browser Use API server...")
    logger.info(f"Server configuration: host={app_config.server.host}, port={app_config.server.port}")

    uvicorn.run(
        app,
        host=app_config.server.host,
        port=app_config.server.port
    )
