import datetime
import asyncio
import uuid
import time
import os
from typing import Dict, Optional, List
from enum import Enum
from dotenv import load_dotenv
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from browser_use import Agent, BrowserSession, BrowserProfile
from browser_use.llm.deepseek.chat import ChatDeepSeek
from patchright.sync_api import ViewportSize

# 导入日志配置
from log_config import setup_logging, get_logger, log_system_info

load_dotenv()

# 设置高级日志系统
setup_logging(
    log_dir="logs",
    log_level=os.getenv("LOG_LEVEL", "INFO"),
    backup_count=int(os.getenv("LOG_BACKUP_COUNT", "30")),
    enable_console_colors=os.getenv("LOG_CONSOLE_COLORS", "true").lower() == "true"
)

# 记录系统信息
log_system_info()

# 创建专用的日志记录器
logger = get_logger("browser_use_api")
request_logger = get_logger("browser_use_api.requests")
business_logger = get_logger("browser_use_api.business")

app = FastAPI(title="Browser Use API", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()

    # 记录请求开始
    request_logger.info(
        f"Request started - Method: {request.method} | "
        f"URL: {request.url} | "
        f"Client IP: {request.client.host if request.client else 'unknown'}"
    )

    # 处理请求
    response = await call_next(request)

    # 计算处理时间
    process_time = time.time() - start_time

    # 记录请求完成
    request_logger.info(
        f"Request completed - Method: {request.method} | "
        f"URL: {request.url} | "
        f"Status: {response.status_code} | "
        f"Process time: {process_time:.3f}s"
    )

    return response

class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TaskRequest(BaseModel):
    taskId: str = ""
    task: str
    contextMsg: str = ""

class TaskResponse(BaseModel):
    taskId: str
    status: TaskStatus
    message: str

class TaskInfo(BaseModel):
    taskId: str
    task: str
    contextMsg: str
    status: TaskStatus
    created_at: datetime.datetime
    completed_at: Optional[datetime.datetime] = None
    result: Optional[str] = None
    error: Optional[str] = None

# 全局任务存储
tasks: Dict[str, TaskInfo] = {}
running_tasks: Dict[str, asyncio.Task] = {}

async def execute_browser_task(taskId: str):
    """执行浏览器任务"""
    start_time = time.time()
    business_logger.info(f"Task {taskId} - Starting browser task execution")

    try:
        # 更新任务状态为运行中
        tasks[taskId].status = TaskStatus.RUNNING
        taskInfo = tasks[taskId]
        business_logger.info(f"Task {taskId} - Status updated to RUNNING")
        business_logger.info(f"Task {taskId} - Task description: {taskInfo.task}")
        business_logger.info(f"Task {taskId} - Context message: {taskInfo.contextMsg}")

        # 配置LLM
        business_logger.info(f"Task {taskId} - Configuring LLM (DeepSeek)")
        llm = ChatDeepSeek(
            model='deepseek-v3',
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            api_key="sk-b2edbc1bf710431995f4331949da5e90"
        )
        business_logger.info(f"Task {taskId} - LLM configuration completed")

        # 配置浏览器
        business_logger.info(f"Task {taskId} - Configuring browser session")
        cdp_url = "http://localhost:9921"
        mobile_profile = BrowserProfile(
            is_mobile=True,
            has_touch=True,
            viewport=ViewportSize(width=720, height=1280),
            user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
        )

        browser_session = BrowserSession(cdp_url=cdp_url, browser_profile=mobile_profile)
        business_logger.info(f"Task {taskId} - Browser session configured with mobile profile")

        # 创建Agent
        business_logger.info(f"Task {taskId} - Creating browser agent")
        agent = Agent(
            task=taskInfo.task,
            llm=llm,
            use_vision=False,
            use_thinking=True,
            browser_session=browser_session,
            save_conversation_path="logs",
            taskId=taskId,
            retry_delay=10,
            message_context=taskInfo.contextMsg,
        )
        business_logger.info(f"Task {taskId} - Agent created successfully")

        # 执行任务
        business_logger.info(f"Task {taskId} - Starting agent execution")
        history = await agent.run()
        execution_time = time.time() - start_time
        business_logger.info(f"Task {taskId} - Agent execution completed in {execution_time:.3f}s")

        # 更新任务状态
        tasks[taskId].status = TaskStatus.COMPLETED
        tasks[taskId].completed_at = datetime.datetime.now()
        tasks[taskId].result = history.final_result()
        business_logger.info(f"Task {taskId} - Task completed successfully")
        business_logger.info(f"Task {taskId} - Final result: {tasks[taskId].result}")

    except asyncio.CancelledError:
        execution_time = time.time() - start_time
        business_logger.warning(f"Task {taskId} - Task was cancelled after {execution_time:.3f}s")
        tasks[taskId].status = TaskStatus.CANCELLED
        tasks[taskId].completed_at = datetime.datetime.now()
        raise
    except Exception as e:
        execution_time = time.time() - start_time
        business_logger.error(f"Task {taskId} - Task failed after {execution_time:.3f}s with error: {str(e)}")
        tasks[taskId].status = TaskStatus.FAILED
        tasks[taskId].completed_at = datetime.datetime.now()
        tasks[taskId].error = str(e)
    finally:
        # 清理运行中的任务
        if taskId in running_tasks:
            del running_tasks[taskId]
            business_logger.info(f"Task {taskId} - Cleaned up from running tasks")

@app.post("/run-task", response_model=TaskResponse)
async def create_task(request: TaskRequest):
    """创建并执行新任务"""
    logger.info("POST /run-task - Creating new browser task")

    taskId = request.taskId
    if len(taskId) == 0:
        taskId = str(uuid.uuid4())
        logger.info(f"Generated new taskId: {taskId}")
    else:
        logger.info(f"Using provided taskId: {taskId}")

    # 检查任务是否已存在
    if taskId in tasks:
        logger.warning(f"Task {taskId} already exists")
        raise HTTPException(status_code=400, detail="Task with this ID already exists")

    logger.info(f"Task {taskId} - Creating task with description: {request.task}")
    if request.contextMsg:
        logger.info(f"Task {taskId} - Context message: {request.contextMsg}")

    # 创建任务信息
    task_info = TaskInfo(
        taskId=taskId,
        task=request.task,
        contextMsg=request.contextMsg,
        status=TaskStatus.PENDING,
        created_at=datetime.datetime.now()
    )
    tasks[taskId] = task_info
    logger.info(f"Task {taskId} - Task info created and stored")

    # 在后台执行任务
    task = asyncio.create_task(
        execute_browser_task(taskId)
    )
    running_tasks[taskId] = task
    logger.info(f"Task {taskId} - Background task started")

    response = TaskResponse(
        taskId=taskId,
        status=TaskStatus.PENDING,
        message="Task created and started"
    )
    logger.info(f"Task {taskId} - Returning response: {response.model_dump()}")
    return response

@app.get("/tasks/{taskId}", response_model=TaskInfo)
async def get_task(taskId: str):
    """查询任务状态"""
    logger.info(f"GET /tasks/{taskId} - Querying task status")

    if taskId not in tasks:
        logger.warning(f"Task {taskId} not found")
        raise HTTPException(status_code=404, detail="Task not found")

    task_info = tasks[taskId]
    logger.info(f"Task {taskId} - Current status: {task_info.status}")
    return task_info

@app.get("/tasks", response_model=List[TaskInfo])
async def list_tasks():
    """获取所有任务列表"""
    logger.info("GET /tasks - Listing all tasks")
    task_list = list(tasks.values())
    logger.info(f"Found {len(task_list)} tasks")

    # 记录各状态的任务数量
    status_counts = {}
    for task in task_list:
        status = task.status
        status_counts[status] = status_counts.get(status, 0) + 1

    logger.info(f"Task status distribution: {status_counts}")
    return task_list

@app.delete("/tasks/{taskId}")
async def cancel_task(taskId: str):
    """取消任务"""
    logger.info(f"DELETE /tasks/{taskId} - Attempting to cancel task")

    if taskId not in tasks:
        logger.warning(f"Task {taskId} not found for cancellation")
        raise HTTPException(status_code=404, detail="Task not found")

    task_info = tasks[taskId]
    logger.info(f"Task {taskId} - Current status: {task_info.status}")

    if task_info.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
        logger.warning(f"Task {taskId} - Cannot cancel task with status: {task_info.status}")
        raise HTTPException(status_code=400, detail="Task already finished")

    # 取消运行中的任务
    if taskId in running_tasks:
        logger.info(f"Task {taskId} - Cancelling running task")
        running_tasks[taskId].cancel()

    task_info.status = TaskStatus.CANCELLED
    task_info.completed_at = datetime.datetime.now()
    logger.info(f"Task {taskId} - Task cancelled successfully")

    return {"message": "Task cancelled"}

@app.get("/")
async def root():
    """根路径"""
    logger.info("GET / - Health check endpoint accessed")
    return {
        "message": "Browser Use API is running",
        "version": "1.0.0",
        "status": "healthy",
        "total_tasks": len(tasks),
        "running_tasks": len(running_tasks)
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    logger.info("GET /health - Health check endpoint accessed")
    return {
        "status": "healthy",
        "timestamp": datetime.datetime.now().isoformat(),
        "total_tasks": len(tasks),
        "running_tasks": len(running_tasks),
        "task_status_counts": {
            status.value: len([t for t in tasks.values() if t.status == status])
            for status in TaskStatus
        }
    }

if __name__ == "__main__":
    import uvicorn
    logger.info("Starting Browser Use API server...")
    logger.info("Server configuration: host=0.0.0.0, port=8080")
    uvicorn.run(app, host="0.0.0.0", port=8080)