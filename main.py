import sys
import datetime
import asyncio
import uuid
from typing import Dict, Optional, List
from enum import Enum
from dotenv import load_dotenv
from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from pydantic import BaseModel
from browser_use import Agent, BrowserSession, BrowserProfile
from browser_use.llm.deepseek.chat import Chat<PERSON>eepSeek
from patchright.sync_api import ViewportSize

load_dotenv()

app = FastAPI(title="Browser Use API", version="1.0.0")

class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TaskRequest(BaseModel):
    taskId: str = ""
    task: str
    contextMsg: str = ""

class TaskResponse(BaseModel):
    taskId: str
    status: TaskStatus
    message: str

class TaskInfo(BaseModel):
    taskId: str
    task: str
    contextMsg: str
    status: TaskStatus
    created_at: datetime.datetime
    completed_at: Optional[datetime.datetime] = None
    result: Optional[str] = None
    error: Optional[str] = None

# 全局任务存储
tasks: Dict[str, TaskInfo] = {}
running_tasks: Dict[str, asyncio.Task] = {}

async def execute_browser_task(taskId: str):
    """执行浏览器任务"""
    try:

        tasks[taskId].status = TaskStatus.RUNNING
        taskInfo = tasks[taskId]
        
        # 配置LLM
        llm = ChatDeepSeek(
            model='deepseek-v3',
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1", 
            api_key="sk-b2edbc1bf710431995f4331949da5e90"
        )
        
        # 配置浏览器
        cdp_url = "http://localhost:9921"
        mobile_profile = BrowserProfile(
            is_mobile=True,
            has_touch=True,
            viewport=ViewportSize(width=720, height=1280),
            user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
        )
        
        browser_session = BrowserSession(cdp_url=cdp_url, browser_profile=mobile_profile)
        
        # 创建Agent
        agent = Agent(
            task=taskInfo.task,
            llm=llm,
            use_vision=False,
            use_thinking=True,
            browser_session=browser_session,
            save_conversation_path="BULogs",
            taskId=taskId,
            retry_delay=10,
            message_context=taskInfo.contextMsg,
        )
        
        # 执行任务
        history = await agent.run()
        
        # 更新任务状态
        tasks[taskId].status = TaskStatus.COMPLETED
        tasks[taskId].completed_at = datetime.datetime.now()
        tasks[taskId].result = history.final_result()
        
    except asyncio.CancelledError:
        tasks[taskId].status = TaskStatus.CANCELLED
        tasks[taskId].completed_at = datetime.datetime.now()
        raise
    except Exception as e:
        tasks[taskId].status = TaskStatus.FAILED
        tasks[taskId].completed_at = datetime.datetime.now()
        tasks[taskId].error = str(e)
    finally:
        # 清理运行中的任务
        if taskId in running_tasks:
            del running_tasks[taskId]

@app.post("/run-task", response_model=TaskResponse)
async def create_task(request: TaskRequest, background_tasks: BackgroundTasks):
    """创建并执行新任务"""
    taskId = request.taskId
    if len(taskId) == 0 :
        taskId = str(uuid.uuid4())

    # 创建任务信息
    task_info = TaskInfo(
        taskId=taskId,
        task=request.task,
        contextMsg=request.contextMsg,
        status=TaskStatus.PENDING,
        created_at=datetime.datetime.now()
    )
    tasks[taskId] = task_info
    
    # 在后台执行任务
    task = asyncio.create_task(
        execute_browser_task(taskId)
    )
    running_tasks[taskId] = task
    
    return TaskResponse(
        taskId=taskId,
        status=TaskStatus.PENDING,
        message="Task created and started"
    )

@app.get("/tasks/{taskId}", response_model=TaskInfo)
async def get_task(taskId: str):
    """查询任务状态"""
    if taskId not in tasks:
        raise HTTPException(status_code=404, detail="Task not found")
    
    return tasks[taskId]

@app.get("/tasks", response_model=List[TaskInfo])
async def list_tasks():
    """获取所有任务列表"""
    return list(tasks.values())

@app.delete("/tasks/{taskId}")
async def cancel_task(taskId: str):
    """取消任务"""
    if taskId not in tasks:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task_info = tasks[taskId]
    
    if task_info.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
        raise HTTPException(status_code=400, detail="Task already finished")
    
    # 取消运行中的任务
    if taskId in running_tasks:
        running_tasks[taskId].cancel()
    
    task_info.status = TaskStatus.CANCELLED
    task_info.completed_at = datetime.datetime.now()
    
    return {"message": "Task cancelled"}

@app.get("/")
async def root():
    """根路径"""
    return {"message": "Browser Use API is running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080)