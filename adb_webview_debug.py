import subprocess
import re
import sys
import logging
import os
import requests
from datetime import datetime

# 配置日志
def setup_logger(log_file=None):
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)

    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)
    ch_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    ch.setFormatter(ch_formatter)
    logger.addHandler(ch)

    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        fh = logging.FileHandler(log_file)
        fh.setLevel(logging.DEBUG)
        fh_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(module)s - %(message)s')
        fh.setFormatter(fh_formatter)
        logger.addHandler(fh)

    return logger

# 执行命令并记录日志
def run_cmd(logger, *cmd):
    cmd_str = ' '.join(cmd)
    logger.debug(f"Executing command: {cmd_str}")
    try:
        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace'
        )
        if result.stdout:
            logger.debug(f"Command stdout: {result.stdout.strip()}")
        if result.stderr:
            logger.debug(f"Command stderr: {result.stderr.strip()}")

        if result.returncode != 0:
            logger.error(f"Error executing command: {cmd_str}")
            logger.error(f"stderr: {result.stderr.strip()}")
            return None

        return result.stdout
    except Exception as e:
        logger.error(f"Exception while executing command: {cmd_str}")
        logger.exception(e)
        return None

# 获取进程列表
def get_process_list(logger, serial):
    logger.info("Fetching process list...")
    output = run_cmd(logger, "adb", "-s", serial, "shell", "ps", "-A")
    if not output:
        return []

    processes = []
    for line in output.strip().splitlines()[1:]:
        parts = line.split()
        if len(parts) >= 9:
            pid = parts[1]
            process_name = parts[-1]
            processes.append({"pid": pid, "name": process_name})
    logger.info(f"Found {len(processes)} processes.")
    return processes

# 获取 WebView 套接字
# def get_webview_sockets(logger, serial):
#     logger.info("Searching for WebView sockets...")
#     output = run_cmd(logger, "adb", "-s", serial, "shell", "grep", "-a", "webview_devtools_remote", "/proc/net/unix")
#     if not output:
#         return []

#     sockets = []
#     for line in output.strip().splitlines():
#         match = re.search(r"@webview_devtools_remote_(\d+)", line)
#         # match = re.search(r"@webview_devtools_remote_(\d+).*?(\d+)", line)
#         if match:
#             socket_name = f"@webview_devtools_remote_{match.group(1)}"
#             pid = match.group(2)
#             sockets.append({"socket": socket_name, "pid": pid})
#     logger.info(f"Found {len(sockets)} WebView sockets.")
#     return sockets

def get_webview_sockets(logger, serial):
    logger.info("Searching for WebView sockets...")
    output = run_cmd(logger, "adb", "-s", serial, "shell", "grep", "-a", "webview_devtools_remote", "/proc/net/unix")
    if not output:
        return []

    sockets = []
    for line in output.strip().splitlines():
        # 提取 socket 名称中的数字（即 WebView ID）
        match = re.search(r"@webview_devtools_remote_(\d+)", line)
        if match:
            webview_id = match.group(1)
            socket_name = f"webview_devtools_remote_{webview_id}"
            # 注意：这里只是 socket 名称，不一定是 PID
            sockets.append({
                "socket": socket_name,
                "pid": webview_id
            })
    logger.info(f"Found {len(sockets)} WebView sockets.")
    return sockets

# 匹配 WebView 套接字与进程
def match_webviews_with_processes(logger, processes, sockets):
    logger.info(f"Matching WebView sockets with processes:{sockets}", )
    logger.info(f"Matching WebView sockets with processes:{processes}", )

    matched = []
    for sock in sockets:
        pid = sock["pid"]
        matched_proc = next((p for p in processes if p["pid"] == pid), None)
        if matched_proc:
            matched.append({
                "pid": pid,
                "process_name": matched_proc["name"],
                "socket": sock["socket"]
            })
    logger.info(f"Matched {len(matched)} WebView processes.")
    return matched

# 打印所有匹配的 WebView 进程
def print_webview_processes(logger, matched_webviews):
    if not matched_webviews:
        logger.warning("No matching WebView processes found.")
        return

    logger.info("WebView processes found:")
    print("\nIndex | PID  | Process Name                      | Socket")
    print("-" * 80)
    for i, item in enumerate(matched_webviews):
        print(f"{i + 1:<6} | {item['pid']:<5} | {item['process_name']:<30} | {item['socket']}")
    print()

# 用户选择 WebView 进程
def select_webview_process(logger, matched_webviews):
    print_webview_processes(logger, matched_webviews)

    if not matched_webviews:
        return None

    if len(matched_webviews) == 1:
        # 只有一个匹配项，自动选择
        item = matched_webviews[0]
        logger.info(f"Auto-selected only available WebView process: PID={item['pid']}, Socket={item['socket']}")
        return item
    
    try:
        choice = int(input("Enter the index of the WebView process to debug (1-based): "))
        if 1 <= choice <= len(matched_webviews):
            return matched_webviews[choice - 1]
        else:
            logger.error("Invalid selection.")
            return None
    except ValueError:
        logger.error("Invalid input.")
        return None

# 设置端口转发
def forward_port(logger, serial, socket_name, local_port=9921):
    logger.info(f"Forwarding local port {local_port} to {socket_name}...")
    result = run_cmd(logger, "adb", "-s", serial, "forward", f"tcp:{local_port}", f"localabstract:{socket_name}")
    if result is None:
        logger.error("Port forwarding failed.")
        return False
    logger.info(f"Port {local_port} forwarded successfully.")
    return True

# 获取调试目标
def get_debug_targets(logger, local_port=9921):
    url = f"http://localhost:{local_port}/json/list"
    logger.info(f"Fetching debug targets from {url}...")
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            targets = response.json()
            logger.info(f"Successfully fetched {len(targets)} debug targets.")
            return targets
        else:
            logger.error(f"Failed to fetch debug targets. Status code: {response.status_code}")
            logger.debug(f"Response: {response.text}")
    except Exception as e:
        logger.error("Error connecting to debugger.")
        logger.exception(e)
    return None

# 主函数
def main(serial, local_port=9921, log_file=None):
    logger = setup_logger(log_file)
    logger.info("=== ADB WebView Debug Script (With Device Serial) Started ===")

    processes = get_process_list(logger, serial)
    sockets = get_webview_sockets(logger, serial)

    matched_webviews = match_webviews_with_processes(logger, processes, sockets)

    selected = select_webview_process(logger, matched_webviews)
    if not selected:
        logger.error("No WebView process selected.")
        return

    logger.info(f"Selected process: PID={selected['pid']}, Name={selected['process_name']}, Socket={selected['socket']}")

    if not forward_port(logger, serial, selected["socket"], local_port):
        logger.error("Port forwarding failed.")
        return

    targets = get_debug_targets(logger, local_port)
    if targets:
        logger.info("Available debug targets:")
        for i, target in enumerate(targets, start=1):
            logger.info(f"Target {i}:")
            logger.info(f"  Title: {target.get('title', 'N/A')}")
            logger.info(f"  URL: {target.get('url', 'N/A')}")
            logger.info(f"  Frontend URL: {target.get('devtoolsFrontendUrl', 'N/A')}")
            logger.info(f"  WebSocket URL: {target.get('webSocketDebuggerUrl', 'N/A')}")
            logger.info("-" * 60)
    else:
        logger.warning("No debug targets found. Make sure WebView debugging is enabled.")

    logger.info("=== ADB WebView Debug Script Finished ===")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python adb_webview_debug_with_serial.py <device_serial> [local_port] [log_file]")
        print("Example: python adb_webview_debug_with_serial.py ***********:4631")
        print("Example with log file: python adb_webview_debug_with_serial.py ***********:4631 9921 debug.log")
        sys.exit(1)

    serial = sys.argv[1]
    local_port = int(sys.argv[2]) if len(sys.argv) > 2 else 9921
    log_file = sys.argv[3] if len(sys.argv) > 3 else None

    main(serial, local_port, log_file)