from playwright.sync_api import sync_playwright
import requests

def main():
    print("Hello from playwtight-demo!")


with sync_playwright() as p:
    cdp_url = "http://localhost:9921"
    # 获取可用页面
    json_list = requests.get(cdp_url + "/json").json()
    print("可用页面：" + str(len(json_list)))
    for i, target in enumerate(json_list):
        print(f"[{i}] {target['title']} -> {target['url']}")

    target_ws = json_list[0]["webSocketDebuggerUrl"]  # 🔗 指向某个页面的 WebSocket
    print("WebSocket URL:", target_ws, "\n")

    # 如果你想连接第 0 个页面
    headers = {"User-Agent": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8"}
    browser = p.chromium.connect_over_cdp(cdp_url)

    context = browser.contexts[0]
    page = context.pages[0]

    print("当前标题：", page.title())
    print("当前URL：", page.url)

    print("当前URL内容：", page.content())

    # 进行操作，比如点击、输入
    page.click("text=同意")



# if __name__ == "__main__":
#     main()
