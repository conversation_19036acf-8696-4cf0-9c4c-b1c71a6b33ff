#!/usr/bin/env python3
"""
Browser Use API 快速启动脚本
提供便捷的启动、测试和管理功能
"""

import os
import sys
import argparse
import subprocess
import time
import requests
from pathlib import Path


def check_dependencies():
    """检查依赖项"""
    print("检查依赖项...")
    
    required_files = [
        "main.py",
        "log_config.py", 
        "log_manager.py",
        "test_api.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有必要文件存在")
    return True


def setup_environment():
    """设置环境"""
    print("设置环境...")
    
    # 创建日志目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
        print(f"✅ 创建日志目录: {log_dir}")
    
    # 检查.env文件
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            print("📝 .env文件不存在，请根据.env.example创建配置文件")
            print("   cp .env.example .env")
        else:
            print("⚠️  .env.example文件不存在")
    else:
        print("✅ .env配置文件存在")
    
    return True


def start_api_server(host="0.0.0.0", port=8080, background=False):
    """启动API服务器"""
    print(f"启动API服务器 (http://{host}:{port})...")
    
    if background:
        # 后台启动
        process = subprocess.Popen([
            sys.executable, "main.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待服务器启动
        print("等待服务器启动...")
        for i in range(30):  # 最多等待30秒
            try:
                response = requests.get(f"http://{host}:{port}/health", timeout=1)
                if response.status_code == 200:
                    print("✅ API服务器启动成功")
                    return process
            except:
                pass
            time.sleep(1)
            print(f"  等待中... ({i+1}/30)")
        
        print("❌ API服务器启动超时")
        process.terminate()
        return None
    else:
        # 前台启动
        subprocess.run([sys.executable, "main.py"])
        return None


def test_api():
    """测试API"""
    print("运行API测试...")
    result = subprocess.run([sys.executable, "test_api.py"])
    return result.returncode == 0


def show_logs(log_type="main", lines=50):
    """显示日志"""
    print(f"显示{log_type}日志 (最后{lines}行)...")
    subprocess.run([sys.executable, "log_manager.py", "tail", "--type", log_type, "--lines", str(lines)])


def analyze_logs(days=7):
    """分析日志"""
    print(f"分析最近{days}天的日志...")
    subprocess.run([sys.executable, "log_manager.py", "analyze", "--days", str(days)])


def clean_logs(days=30, dry_run=True):
    """清理日志"""
    action = "预览" if dry_run else "执行"
    print(f"{action}清理{days}天前的日志...")
    
    cmd = [sys.executable, "log_manager.py", "clean", "--days", str(days)]
    if not dry_run:
        cmd.append("--no-dry-run")
    
    subprocess.run(cmd)


def show_status():
    """显示系统状态"""
    print("=== Browser Use API 系统状态 ===")
    
    # 检查API服务器
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ API服务器: 运行中")
            print(f"   总任务数: {data.get('total_tasks', 0)}")
            print(f"   运行中任务: {data.get('running_tasks', 0)}")
        else:
            print("❌ API服务器: 响应异常")
    except:
        print("❌ API服务器: 未运行")
    
    # 检查日志文件
    log_dir = "logs"
    if os.path.exists(log_dir):
        log_files = list(Path(log_dir).glob("*.log"))
        print(f"📁 日志文件: {len(log_files)} 个")
        
        total_size = sum(f.stat().st_size for f in log_files)
        print(f"   总大小: {total_size / 1024 / 1024:.2f}MB")
    else:
        print("📁 日志文件: 目录不存在")


def main():
    parser = argparse.ArgumentParser(description="Browser Use API 快速启动脚本")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 启动服务器
    start_parser = subparsers.add_parser("start", help="启动API服务器")
    start_parser.add_argument("--host", default="0.0.0.0", help="服务器主机")
    start_parser.add_argument("--port", type=int, default=8080, help="服务器端口")
    start_parser.add_argument("--background", action="store_true", help="后台运行")
    
    # 测试API
    subparsers.add_parser("test", help="运行API测试")
    
    # 显示日志
    logs_parser = subparsers.add_parser("logs", help="显示日志")
    logs_parser.add_argument("--type", default="main", 
                           choices=["main", "error", "request", "business"],
                           help="日志类型")
    logs_parser.add_argument("--lines", type=int, default=50, help="显示行数")
    
    # 分析日志
    analyze_parser = subparsers.add_parser("analyze", help="分析日志")
    analyze_parser.add_argument("--days", type=int, default=7, help="分析天数")
    
    # 清理日志
    clean_parser = subparsers.add_parser("clean", help="清理日志")
    clean_parser.add_argument("--days", type=int, default=30, help="保留天数")
    clean_parser.add_argument("--execute", action="store_true", help="实际执行清理")
    
    # 显示状态
    subparsers.add_parser("status", help="显示系统状态")
    
    # 设置环境
    subparsers.add_parser("setup", help="设置环境")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        print("\n常用命令:")
        print("  python start.py setup          # 设置环境")
        print("  python start.py start          # 启动API服务器")
        print("  python start.py test           # 测试API")
        print("  python start.py status         # 查看状态")
        print("  python start.py logs           # 查看日志")
        print("  python start.py analyze        # 分析日志")
        return
    
    # 检查依赖项
    if not check_dependencies():
        return
    
    if args.command == "setup":
        setup_environment()
    elif args.command == "start":
        setup_environment()
        start_api_server(args.host, args.port, args.background)
    elif args.command == "test":
        test_api()
    elif args.command == "logs":
        show_logs(args.type, args.lines)
    elif args.command == "analyze":
        analyze_logs(args.days)
    elif args.command == "clean":
        clean_logs(args.days, not args.execute)
    elif args.command == "status":
        show_status()


if __name__ == "__main__":
    main()
