"""
API路由定义
包含所有API端点的路由处理逻辑
"""

import datetime
from typing import List
from fastapi import APIRouter, HTTPException

from models import (
    RunTaskReq, RunIntentTaskReq, TaskInfo, TaskRes,
    HealthResponse, RootResponse
)
from task_manager import task_manager
from log_config import get_logger

# 创建专用的日志记录器
logger = get_logger("browser_use_api")

# 创建路由器
router = APIRouter()


@router.post("/api/aia/auto/run-task", response_model=TaskRes)
async def create_task(req: RunTaskReq):
    """创建并执行新任务"""
    logger.info("POST /run-task - Creating new browser task")

    return await task_manager.create_task(
        task_id=req.arg.taskId,
        task_description=req.arg.task,
        ext_sys_msg=req.arg.extSysMsg,
        adbDevice=req.arg.adbDevice
    )


@router.post("/api/aia/auto/run-intent-task", response_model=TaskRes)
async def create_extended_task(req: RunIntentTaskReq):
    """基于意图请求创建并执行新任务"""
    logger.info("POST /run-intent-task - Creating new browser task from intent request")
    
    # 从扩展请求结构构建任务描述
    intent = req.arg.intent
    task_description = (
        f'我的目标是 {intent.action}，我已经打开了{intent.site}, '
        f'并且搜索了 {intent.arg.drinkName}，现在进入到了商品选择页，'
        f'请帮我选择符合我口味的商品，选择规格后，加入购物车，最后去结算即可。点击了"去结算"按钮或者看到了"提交订单"按钮就表示任务完成了，不要点击提交订单。'
    )
    if len(intent.arg.options) > 0:
        task_description += f'我想要的是: {intent.arg.options}'

    # 1.在商品列表页，先点击商品名称查看商品详情后，再点击"加入购物车"选规格，而不要直接在搜索页点击"选规格"或加号按钮。
    # 1.选完规格后，如果出现"未选必选品"类似的按钮，则需要进入商品详情，继续操作。
    ext_sys_msg = """有以下重要说明：
    1.如果没有找到符合我口味的，使用默认选项即可。
    2.点击加入购物车后，可能会出现"未选必选品"按钮，请点击这个"未选必选品"按钮，会自动跳转到必选品页面。如果用户没有强调，则默认选择需要吸管。
    3.只在当前页面操作，不要新建页面。
    """

    logger.info(f"Creating intent task with description: {task_description}")

    return await task_manager.create_task(
        task_id=req.arg.taskId,
        task_description=task_description,
        ext_sys_msg=ext_sys_msg,
        adbDevice=req.arg.adbDevice

    )


@router.get("/api/aia/auto/tasks/{taskId}", response_model=TaskInfo)
async def get_task(taskId: str):
    """查询任务状态"""
    logger.info(f"GET /tasks/{taskId} - Querying task status")
    
    task_info = task_manager.get_task(taskId)
    if not task_info:
        logger.warning(f"Task {taskId} not found")
        raise HTTPException(status_code=404, detail="Task not found")
    
    logger.info(f"Task {taskId} - Current status: {task_info.status}")
    return task_info


@router.get("/api/aia/auto/tasks", response_model=List[TaskInfo])
async def list_tasks():
    """获取所有任务列表"""
    logger.info("GET /tasks - Listing all tasks")
    task_list = task_manager.get_all_tasks()
    logger.info(f"Found {len(task_list)} tasks")
    
    # 记录各状态的任务数量
    status_counts = {}
    for task in task_list:
        status = task.status
        status_counts[status] = status_counts.get(status, 0) + 1
    
    logger.info(f"Task status distribution: {status_counts}")
    return task_list


@router.delete("/api/aia/auto/tasks/{taskId}")
async def cancel_task(taskId: str):
    """取消任务"""
    logger.info(f"DELETE /tasks/{taskId} - Attempting to cancel task")
    return await task_manager.cancel_task(taskId)


@router.get("/", response_model=RootResponse)
async def root():
    """根路径"""
    logger.info("GET / - Root endpoint accessed")
    return RootResponse(
        message="Browser Use API is running",
        version="1.0.0",
        status="healthy",
        total_tasks=task_manager.get_total_task_count(),
        running_tasks=task_manager.get_running_task_count()
    )


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    logger.info("GET /health - Health check endpoint accessed")
    return HealthResponse(
        status="healthy",
        timestamp=datetime.datetime.now().isoformat(),
        total_tasks=task_manager.get_total_task_count(),
        running_tasks=task_manager.get_running_task_count(),
        task_status_counts=task_manager.get_task_status_counts()
    )
