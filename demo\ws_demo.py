import asyncio
import websockets
import json
import requests

async def connect_to_cdp_page(cdp_url):
    """
    连接到CDP页面并执行Runtime命令
    """
    if not cdp_url.endswith('/'):
        cdp_url += '/'
      
    # 1. 获取可用的页面目标
    response = requests.get(f"{cdp_url}json")
    targets = response.json()
              
    # 找到第一个页面类型的目标
    page_target = None
    for target in targets:
        if target.get('type') == 'page':
            page_target = target
            break
      
    if not page_target:
        raise Exception("No page target found")
      
    print(f"Connecting to page: {page_target['title']}")
    ws_url = page_target['webSocketDebuggerUrl']
      
    # 2. 连接到页面的WebSocket
    websocket = await websockets.connect(ws_url)
      
    # 3. 启用Runtime域
    await websocket.send(json.dumps({
        "id": 1,
        "method": "Runtime.enable"
    }))
      
    response = await websocket.recv()
    print(f"Runtime.enable response: {response}")
      
    return websocket

async def main():
    # 连接到页面
    ws = await connect_to_cdp_page("http://localhost:9921")
      
    # 执行JavaScript代码
    await ws.send(json.dumps({
        "id": 2,
        "method": "Runtime.evaluate",
        "params": {
            "expression": "document.title",
            "returnByValue": True
        }
    }))
      
    result = await ws.recv()
    print(f"Page title result: {result}")
      
    await ws.close()

if __name__ == "__main__":
    asyncio.run(main())