import sys
import datetime
import asyncio
from dotenv import load_dotenv
from browser_use import Agent, BrowserSession, BrowserConfig, <PERSON><PERSON><PERSON>  ,BrowserProfile
from browser_use.llm.openai.chat import ChatOpenA<PERSON>
from browser_use.llm.deepseek.chat import Chat<PERSON><PERSON><PERSON>eek
from browser_use.llm.groq.chat import Chat<PERSON><PERSON><PERSON>
from patchright.sync_api import ViewportSize

load_dotenv()

contextMsg = ""


async def main(task, contextMsg):
    # llm=ChatDeepSeek(model='deepseek-chat', api_key="***********************************")
    # llm=ChatGroq(model='deepseek-v3-250324',base_url="https://ark.cn-beijing.volces.com/api/v3", api_key="1444135e-36b0-4f40-9130-7524530ea32c")
    # llm=ChatGroq(model='deepseek-r1-250528',base_url="https://ark.cn-beijing.volces.com/api/v3", api_key="1444135e-36b0-4f40-9130-7524530ea32c")
    # llm=ChatGroq(model='kimi-k2-250711',base_url="https://ark.cn-beijing.volces.com/api/v3", api_key="1444135e-36b0-4f40-9130-7524530ea32c")
    # llm=ChatGroq(model='doubao-seed-1-6-250615',base_url="https://ark.cn-beijing.volces.com/api/v3", api_key="1444135e-36b0-4f40-9130-7524530ea32c")
    # llm=ChatGroq(model='kimi-k2-0711-preview',base_url="https://api.moonshot.cn/v1", api_key="sk-jA00vezbkJ9QxLPFO7MPvjBVqE48IVeQj5LeOoWj8rHpkuSi")
    # 
    # llm=ChatGroq(model='moonshotai/Kimi-K2-Instruct',base_url="https://api-inference.modelscope.cn/v1/", api_key="ms-4cfeab3f-b872-41ee-ad8e-da67287771d8")

    # ok
    # llm=ChatGroq(model='qwen3-coder-plus',base_url="https://dashscope.aliyuncs.com/compatible-mode/v1", api_key="sk-b2edbc1bf710431995f4331949da5e90")
    # llm=ChatGroq(model='qwen3-235b-a22b-instruct-2507',base_url="https://dashscope.aliyuncs.com/compatible-mode/v1", api_key="sk-b2edbc1bf710431995f4331949da5e90")
    # llm=ChatGroq(model='qwen-turbo-latest',base_url="https://dashscope.aliyuncs.com/compatible-mode/v1", api_key="sk-b2edbc1bf710431995f4331949da5e90")
    llm=ChatDeepSeek(model='deepseek-v3',base_url="https://dashscope.aliyuncs.com/compatible-mode/v1", api_key="sk-b2edbc1bf710431995f4331949da5e90")
    # llm=ChatDeepSeek(model='Moonshot-Kimi-K2-Instruct',base_url="https://dashscope.aliyuncs.com/compatible-mode/v1", api_key="sk-b2edbc1bf710431995f4331949da5e90")


    cdp_url = "http://localhost:9921"
    mobile_profile = BrowserProfile(  
        # **playwright.devices['iPhone 13'],  # 自动设置移动端参数  
        # 或者手动设置：  
        is_mobile=True,  
        has_touch=True,  
        viewport=ViewportSize(width=720, height=1280),
        user_agent='Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',  
        # device_scale_factor=2.0
    ) 
    browser_session = BrowserSession(cdp_url=cdp_url, browser_profile=mobile_profile)
    # browser_session = BrowserSession(cdp_url=cdp_url)

    agent = Agent(
        task=task,
        llm=llm,
        use_vision=False,
        use_thinking=True,
        browser_session=browser_session,
        save_conversation_path="BULogs",
        task_id=datetime.datetime.now().strftime('%Y%m%d_%H%M%S_%f'),
        retry_delay=10,
        message_context=contextMsg,
    )
    history  = await agent.run()

    print(history.is_done(), history.is_successful())
    print(history.final_result())
    print(history.action_names(), history.action_results())


if __name__ == "__main__":
    
    # task="打开饿了么网站https://h5.ele.me，给我买一杯霸王茶姬的奶茶，，要求大杯，少糖，少冰",
    task="打开饿了么网站https://h5.ele.me，给我买一杯星巴克的冰美式",
    task="打开百度，搜索科技新闻，获取前3条新闻标题和链接"
    # task="我已经打开了qq.com腾讯网，帮我查看财经和科技的前3条新闻"
    # task="我已经打开了商品页面，请按照我的需求选择合适的口味，我的口味是热的，少少糖，然后加入购物车就可以了，不用其他动作"
    task="帮我分析下当前页面内容，有哪些内容，以便我后续操作"
    task="在当前页面，我已经选好了产品，请按我的口味帮我选择，然后加入购物车即可"
    task="在当前页面，我已经选好了产品，请按我的口味帮我选择，然后加入购物车即可。我的口味是大杯，少冰少糖。"
    contextMsg = ""

    if len(sys.argv) >= 2:
        filename = sys.argv[1]
        with open(filename, 'r', encoding='utf-8') as file:
            task = file.read()
    asyncio.run(main(task, contextMsg=contextMsg))

