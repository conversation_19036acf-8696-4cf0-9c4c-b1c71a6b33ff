#!/usr/bin/env python3
"""
ADB连接测试脚本
用于测试ADB设备连接和CDP端口转发功能
"""

import sys
import json
from adb_utils.webview_forward import get_cdp_connection
from config import app_config

def test_adb_connection(device_serial: str, process_name: str = None, local_port: int = None):
    """
    测试ADB连接
    
    Args:
        device_serial: 设备序列号，格式为 ip:port
        process_name: 进程名称，默认使用配置文件中的值
        local_port: 本地端口，默认使用配置文件中的值
    """
    print(f"=== ADB连接测试 ===")
    print(f"设备序列号: {device_serial}")
    
    # 使用配置文件中的默认值
    if process_name is None:
        process_name = app_config.browser.adb_process_name
    if local_port is None:
        local_port = app_config.browser.adb_local_port
    
    print(f"进程名称: {process_name}")
    print(f"本地端口: {local_port}")
    print()
    
    try:
        # 尝试建立连接
        print("正在建立ADB连接...")
        connection_info = get_cdp_connection(
            device_serial=device_serial,
            process_name=process_name,
            local_port=local_port
        )
        
        if connection_info:
            print("✅ ADB连接建立成功!")
            print()
            print("连接信息:")
            print(f"  设备序列号: {connection_info['device_serial']}")
            print(f"  进程名称: {connection_info['process_name']}")
            print(f"  进程PID: {connection_info['pid']}")
            print(f"  WebView Socket: {connection_info['socket_name']}")
            print(f"  本地端口: {connection_info['local_port']}")
            print(f"  调试目标数量: {len(connection_info['targets'])}")
            print()
            
            if connection_info['targets']:
                print("调试目标列表:")
                for i, target in enumerate(connection_info['targets'], 1):
                    print(f"  目标 {i}:")
                    print(f"    标题: {target.get('title', 'N/A')}")
                    print(f"    URL: {target.get('url', 'N/A')}")
                    print(f"    类型: {target.get('type', 'N/A')}")
                    if target.get('webSocketDebuggerUrl'):
                        print(f"    WebSocket调试URL: {target['webSocketDebuggerUrl']}")
                    print()
            
            # 生成CDP URL
            cdp_url = f"http://localhost:{connection_info['local_port']}"
            print(f"CDP连接URL: {cdp_url}")
            print()
            print("✅ 测试完成，连接正常!")
            return True
            
        else:
            print("❌ ADB连接建立失败!")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python test_adb_connection.py <device_serial> [process_name] [local_port]")
        print()
        print("示例:")
        print("  python test_adb_connection.py ***********:4631")
        print("  python test_adb_connection.py ***********:4631 org.chromium.webview_shell")
        print("  python test_adb_connection.py ***********:4631 org.chromium.webview_shell 9222")
        print()
        print("当前配置:")
        print(f"  默认进程名称: {app_config.browser.adb_process_name}")
        print(f"  默认本地端口: {app_config.browser.adb_local_port}")
        sys.exit(1)
    
    device_serial = sys.argv[1]
    process_name = sys.argv[2] if len(sys.argv) > 2 else None
    local_port = int(sys.argv[3]) if len(sys.argv) > 3 else None
    
    success = test_adb_connection(device_serial, process_name, local_port)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
