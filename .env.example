# Browser Use API 环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置值

# ==================== 服务器配置 ====================
# 服务器监听地址
SERVER_HOST=0.0.0.0

# 服务器监听端口
SERVER_PORT=8080

# API标题
API_TITLE=Browser Use API

# API版本
API_VERSION=1.0.0

# ==================== LLM配置 ====================
# LLM模型名称
LLM_MODEL=deepseek-v3
#LLM_MODEL=qwen3-235b-a22b-instruct-2507
#LLM_MODEL=qwen3-coder-plus
#LLM_MODEL=qwen-turbo-latest
#LLM_MODEL=Moonshot-Kimi-K2-Instruct

# LLM API基础URL
LLM_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# LLM API密钥
LLM_API_KEY=sk-b2edbc1bf710431995f4331949da5e90

# ==================== 浏览器配置 ====================
# 默认CDP连接URL（当不使用ADB时）
BROWSER_CDP_URL=http://localhost:9921

# 是否启用移动设备模式
BROWSER_IS_MOBILE=true

# 是否启用触摸支持
BROWSER_HAS_TOUCH=true

# 视口宽度
BROWSER_VIEWPORT_WIDTH=720

# 视口高度
BROWSER_VIEWPORT_HEIGHT=1280

# 用户代理字符串（留空使用默认值）
BROWSER_USER_AGENT=

# ==================== ADB设备配置 ====================
# ADB端口转发的本地端口
ADB_LOCAL_PORT=9222

# 目标进程名称
ADB_PROCESS_NAME=org.chromium.webview_shell

# ==================== Agent配置 ====================
# 是否启用视觉功能
AGENT_USE_VISION=false

# 是否启用思考模式
AGENT_USE_THINKING=false

# 对话保存路径
AGENT_SAVE_PATH=logs

# 重试延迟（秒）
AGENT_RETRY_DELAY=10

# browser-use上报开关
BROWSER_USE_CLOUD_SYNC=false

# ==================== 日志配置 ====================
# 日志目录
LOG_DIR=logs

# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# 日志文件保留天数
LOG_BACKUP_COUNT=30

# 是否启用控制台彩色输出
LOG_CONSOLE_COLORS=true

