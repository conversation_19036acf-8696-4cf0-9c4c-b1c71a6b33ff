# Browser Use API 环境配置示例
# 复制此文件为 .env 并根据需要修改配置

# ===== 日志配置 =====
# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# 日志文件保留天数
LOG_BACKUP_COUNT=30

# 控制台日志是否启用颜色 (true/false)
LOG_CONSOLE_COLORS=true

# 日志目录
LOG_DIR=logs

# ===== API 配置 =====
# API服务器主机
API_HOST=0.0.0.0

# API服务器端口
API_PORT=8080

# ===== 浏览器配置 =====
# Chrome DevTools Protocol URL
CDP_URL=http://localhost:9921

# ===== LLM 配置 =====
# DeepSeek API配置
DEEPSEEK_MODEL=deepseek-v3
DEEPSEEK_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
DEEPSEEK_API_KEY=your-api-key-here

# ===== 其他配置 =====
# 任务重试延迟（秒）
TASK_RETRY_DELAY=10

# 是否启用视觉功能
USE_VISION=false

# 是否启用思考功能
USE_THINKING=true

# 对话保存路径
CONVERSATION_SAVE_PATH=BULogs
