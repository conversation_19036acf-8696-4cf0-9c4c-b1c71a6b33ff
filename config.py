"""
配置管理模块
包含应用程序的所有配置项和环境变量管理
"""

import os
from typing import Optional
from dotenv import load_dotenv
from pydantic import BaseModel
from patchright.sync_api import ViewportSize
from browser_use import BrowserProfile

# 加载环境变量
load_dotenv()


class LogConfig(BaseModel):
    """日志配置"""
    log_dir: str = "logs"
    log_level: str = "INFO"
    backup_count: int = 30
    enable_console_colors: bool = True


class ServerConfig(BaseModel):
    """服务器配置"""
    host: str = "0.0.0.0"
    port: int = 8080
    title: str = "Browser Use API"
    version: str = "1.0.0"


class LLMConfig(BaseModel):
    """LLM配置"""
    model: str
    base_url: str
    api_key: str


class BrowserConfig(BaseModel):
    """浏览器配置"""
    cdp_url: str = "http://localhost:9921"
    is_mobile: bool = True
    has_touch: bool = True
    viewport_width: int = 720
    viewport_height: int = 1280
    user_agent: str = ''
    # ADB相关配置
    adb_local_port: int = 9222
    adb_process_name: str = "org.chromium.webview_shell"


class AgentConfig(BaseModel):
    """Agent配置"""
    use_vision: bool = False
    use_thinking: bool = False
    save_conversation_path: str = "logs"
    retry_delay: int = 10


class AppConfig(BaseModel):
    """应用程序总配置"""
    log: LogConfig
    server: ServerConfig
    llm: LLMConfig
    browser: BrowserConfig
    agent: AgentConfig


def load_config() -> AppConfig:
    """加载应用程序配置"""
    return AppConfig(
        log=LogConfig(
            log_dir=os.getenv("LOG_DIR", "logs"),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            backup_count=int(os.getenv("LOG_BACKUP_COUNT", "30")),
            enable_console_colors=os.getenv("LOG_CONSOLE_COLORS", "true").lower() == "true"
        ),
        server=ServerConfig(
            host=os.getenv("SERVER_HOST", "0.0.0.0"),
            port=int(os.getenv("SERVER_PORT", "8080")),
            title=os.getenv("API_TITLE", "Browser Use API"),
            version=os.getenv("API_VERSION", "1.0.0")
        ),
        llm=LLMConfig(
            model=os.getenv("LLM_MODEL", "deepseek-v3"),
            base_url=os.getenv("LLM_BASE_URL", ""),
            api_key=os.getenv("LLM_API_KEY", "")
        ),
        browser=BrowserConfig(
            cdp_url=os.getenv("BROWSER_CDP_URL", "http://localhost:9921"),
            is_mobile=os.getenv("BROWSER_IS_MOBILE", "true").lower() == "true",
            has_touch=os.getenv("BROWSER_HAS_TOUCH", "true").lower() == "true",
            viewport_width=int(os.getenv("BROWSER_VIEWPORT_WIDTH", "720")),
            viewport_height=int(os.getenv("BROWSER_VIEWPORT_HEIGHT", "1280")),
            user_agent=os.getenv("BROWSER_USER_AGENT", ''),
            adb_local_port=int(os.getenv("ADB_LOCAL_PORT", "9222")),
            adb_process_name=os.getenv("ADB_PROCESS_NAME", "org.chromium.webview_shell")
        ),
        agent=AgentConfig(
            use_vision=os.getenv("AGENT_USE_VISION", "false").lower() == "true",
            use_thinking=os.getenv("AGENT_USE_THINKING", "false").lower() == "true",
            save_conversation_path=os.getenv("AGENT_SAVE_PATH", "logs"),
            retry_delay=int(os.getenv("AGENT_RETRY_DELAY", "10"))
        )
    )


def get_browser_profile(config: BrowserConfig) -> BrowserProfile:
    """创建浏览器配置文件"""
    return BrowserProfile(
        is_mobile=config.is_mobile,
        has_touch=config.has_touch,
        viewport=ViewportSize(width=config.viewport_width, height=config.viewport_height),
        # user_agent=config.user_agent,
    )


# 全局配置实例
app_config = load_config()
