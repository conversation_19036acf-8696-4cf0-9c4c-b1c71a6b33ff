"""
任务管理模块
包含任务的创建、执行、状态管理等核心业务逻辑
"""

import asyncio
import datetime
import time
import uuid
from typing import Dict, Optional
from fastapi import HTTPException

from browser_use import Agent, BrowserSession
from browser_use.llm.deepseek.chat import Chat<PERSON><PERSON><PERSON><PERSON>

from models import TaskInfo, TaskStatus, TaskRes, TaskResData
from config import app_config, get_browser_profile
from log_config import get_logger
from adb_utils.webview_forward import get_cdp_connection

# 创建专用的日志记录器
logger = get_logger("browser_use_api")
business_logger = get_logger("browser_use_api.business")


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.tasks: Dict[str, TaskInfo] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
    
    def get_task(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务信息"""
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> list[TaskInfo]:
        """获取所有任务"""
        return list(self.tasks.values())
    
    def get_task_status_counts(self) -> dict:
        """获取各状态任务数量统计"""
        return {
            status.value: len([t for t in self.tasks.values() if t.status == status])
            for status in TaskStatus
        }
    
    def get_running_task_count(self) -> int:
        """获取运行中任务数量"""
        return len(self.running_tasks)
    
    def get_total_task_count(self) -> int:
        """获取总任务数量"""
        return len(self.tasks)
    
    async def create_task(self, task_id: str, task_description: str, ext_sys_msg: str, adbDevice: str) -> TaskRes:
        """
        创建并启动新任务
        
        Args:
            task_id: 任务ID，如果为空则自动生成
            task_description: 任务描述
            ext_sys_msg: 扩展系统消息
            
        Returns:
            TaskRes: 任务响应
        """
        logger.info(f"Creating task with ID: {task_id}")
        
        # 生成任务ID（如果未提供）
        if not task_id:
            task_id = str(uuid.uuid4())
            logger.info(f"Generated new taskId: {task_id}")
        
        # 检查任务是否已存在
        if task_id in self.tasks:
            logger.warning(f"Task {task_id} already exists")
            raise HTTPException(status_code=400, detail="Task with this ID already exists")
        
        # 创建任务信息
        task_info = TaskInfo(
            taskId=task_id,
            task=task_description,
            extSysMsg=ext_sys_msg,
            adbDevice=adbDevice,
            status=TaskStatus.PENDING,
            created_at=datetime.datetime.now()
        )
        self.tasks[task_id] = task_info
        logger.info(f"Task {task_id} - Task info created and stored")
        
        # 在后台执行任务
        task = asyncio.create_task(self._execute_browser_task(task_id))
        self.running_tasks[task_id] = task
        logger.info(f"Task {task_id} - Background task started")
        
        # 准备响应
        data = TaskResData(
            taskId=task_id,
            status=TaskStatus.PENDING,
            message="Task created and started"
        )
        res = TaskRes(
            code=0,
            msg="Task created and started",
            data=data
        )
        
        logger.info(f"Task {task_id} - Returning response: {res.model_dump()}")
        return res
    
    async def cancel_task(self, task_id: str) -> dict:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            dict: 取消结果
        """
        logger.info(f"Attempting to cancel task: {task_id}")
        
        if task_id not in self.tasks:
            logger.warning(f"Task {task_id} not found for cancellation")
            raise HTTPException(status_code=404, detail="Task not found")
        
        task_info = self.tasks[task_id]
        logger.info(f"Task {task_id} - Current status: {task_info.status}")
        
        if task_info.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            logger.warning(f"Task {task_id} - Cannot cancel task with status: {task_info.status}")
            raise HTTPException(status_code=400, detail="Task already finished")
        
        # 取消运行中的任务
        if task_id in self.running_tasks:
            logger.info(f"Task {task_id} - Cancelling running task")
            self.running_tasks[task_id].cancel()
        
        task_info.status = TaskStatus.CANCELLED
        task_info.completed_at = datetime.datetime.now()
        logger.info(f"Task {task_id} - Task cancelled successfully")
        
        return {"message": "Task cancelled"}
    
    async def _execute_browser_task(self, task_id: str):
        """
        执行浏览器任务的内部方法
        
        Args:
            task_id: 任务ID
        """
        start_time = time.time()
        business_logger.info(f"Task {task_id} - Starting browser task execution")
        
        try:
            # 更新任务状态为运行中
            self.tasks[task_id].status = TaskStatus.RUNNING
            task_info = self.tasks[task_id]
            business_logger.info(f"Task {task_id} - Status updated to RUNNING")
            business_logger.info(f"Task {task_id} - Task description: {task_info.task}")
            business_logger.info(f"Task {task_id} - extSysMsg: {task_info.extSysMsg}")
            
            # 配置LLM
            business_logger.info(f"Task {task_id} - Configuring LLM (DeepSeek){app_config.llm}")
            llm = ChatDeepSeek(
                model=app_config.llm.model,
                base_url=app_config.llm.base_url,
                api_key=app_config.llm.api_key
            )
            business_logger.info(f"Task {task_id} - LLM configuration completed")
            
            # 配置浏览器
            business_logger.info(f"Task {task_id} - Configuring browser session")

            # 处理ADB设备连接
            if len(task_info.adbDevice) > 0:
                business_logger.info(f"Task {task_id} - Setting up ADB connection for device: {task_info.adbDevice}")

                # 使用ADB工具建立CDP连接
                connection_info = get_cdp_connection(
                    device_serial=task_info.adbDevice,
                    process_name=app_config.browser.adb_process_name,
                    local_port=app_config.browser.adb_local_port
                )

                if connection_info:
                    # 更新CDP URL为本地转发端口
                    cdp_url = f"http://localhost:{connection_info['local_port']}"
                    business_logger.info(f"Task {task_id} - ADB connection established, using CDP URL: {cdp_url}")
                    business_logger.info(f"Task {task_id} - Found {len(connection_info['targets'])} debug targets")
                else:
                    business_logger.error(f"Task {task_id} - Failed to establish ADB connection for device: {task_info.adbDevice}")
                    raise Exception(f"Failed to establish ADB connection for device: {task_info.adbDevice}")
            else:
                # 使用默认CDP URL
                cdp_url = app_config.browser.cdp_url
                business_logger.info(f"Task {task_id} - Using default CDP URL: {cdp_url}")

            # 配置浏览器会话
            business_logger.info(f"Task {task_id} - Configuring browser session")
            browser_profile = get_browser_profile(app_config.browser)
            browser_session = BrowserSession(
                cdp_url=cdp_url,  # 使用动态确定的CDP URL
                browser_profile=browser_profile
            )
            business_logger.info(f"Task {task_id} - Browser session configured with CDP URL: {cdp_url}")
            
            # 创建Agent
            business_logger.info(f"Task {task_id} - Creating browser agent")
            agent = Agent(
                task=task_info.task,
                llm=llm,
                use_vision=app_config.agent.use_vision,
                use_thinking=app_config.agent.use_thinking,
                browser_session=browser_session,
                save_conversation_path=app_config.agent.save_conversation_path,
                task_id=task_id,
                retry_delay=app_config.agent.retry_delay,
                extend_system_message=task_info.extSysMsg,
            )
            business_logger.info(f"Task {task_id} - Agent created successfully")
            
            # 执行任务
            business_logger.info(f"Task {task_id} - Starting agent execution")
            history = await agent.run()
            execution_time = time.time() - start_time
            business_logger.info(f"Task {task_id} - Agent execution completed in {execution_time:.3f}s")
            
            # 更新任务状态
            self.tasks[task_id].status = TaskStatus.COMPLETED
            self.tasks[task_id].completed_at = datetime.datetime.now()
            self.tasks[task_id].result = history.final_result()

            business_logger.info(f"Task {task_id} - screenshots: {history.screenshots()}")
            business_logger.info(f"Task {task_id} - Task completed successfully")
            business_logger.info(f"Task {task_id} - Final result: {self.tasks[task_id].result}")
            

            
        except asyncio.CancelledError:
            execution_time = time.time() - start_time
            business_logger.warning(f"Task {task_id} - Task was cancelled after {execution_time:.3f}s")
            self.tasks[task_id].status = TaskStatus.CANCELLED
            self.tasks[task_id].completed_at = datetime.datetime.now()
            raise
        except Exception as e:
            execution_time = time.time() - start_time
            business_logger.error(f"Task {task_id} - Task failed after {execution_time:.3f}s with error: {str(e)}")
            self.tasks[task_id].status = TaskStatus.FAILED
            self.tasks[task_id].completed_at = datetime.datetime.now()
            self.tasks[task_id].error = str(e)
        finally:
            # 清理运行中的任务
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
                business_logger.info(f"Task {task_id} - Cleaned up from running tasks")


# 全局任务管理器实例
task_manager = TaskManager()
