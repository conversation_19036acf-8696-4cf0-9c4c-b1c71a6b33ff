# webview_cdp_connector.py

import subprocess
import re
import logging
import requests
from typing import Optional, Dict, Any

def setup_logger():
    """Setup basic logger for the module"""
    logger = logging.getLogger(__name__)
    if not logger.handlers:
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    return logger

def run_cmd(logger, *cmd):
    """Execute command and return output"""
    cmd_str = ' '.join(cmd)
    logger.debug(f"Executing command: {cmd_str}")
    try:
        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace'
        )
        if result.stdout:
            logger.debug(f"Command stdout: {result.stdout.strip()}")
        if result.stderr:
            logger.debug(f"Command stderr: {result.stderr.strip()}")

        if result.returncode != 0:
            logger.error(f"Error executing command: {cmd_str}")
            logger.error(f"stderr: {result.stderr.strip()}")
            return None

        return result.stdout
    except Exception as e:
        logger.error(f"Exception while executing command: {cmd_str}")
        logger.exception(e)
        return None

def get_process_pid(logger, serial: str, process_name: str) -> Optional[str]:
    """
    Get the PID of a specific process on the device
    
    Args:
        logger: Logger instance
        serial: Device serial in format ip:port
        process_name: Process name to search for
        
    Returns:
        PID of the process or None if not found
    """
    logger.info(f"Searching for process '{process_name}' on device {serial}")
    output = run_cmd(logger, "adb", "-s", serial, "shell", "ps", "-A")
    if not output:
        return None

    for line in output.strip().splitlines()[1:]:
        parts = line.split()
        if len(parts) >= 9 and parts[-1] == process_name:
            pid = parts[1]
            logger.info(f"Found process '{process_name}' with PID {pid}")
            return pid
    
    logger.warning(f"Process '{process_name}' not found on device {serial}")
    return None

def get_webview_socket(logger, serial: str, pid: str) -> Optional[str]:
    """
    Get WebView socket for a specific PID
    
    Args:
        logger: Logger instance
        serial: Device serial in format ip:port
        pid: Process ID
        
    Returns:
        Socket name or None if not found
    """
    logger.info(f"Searching for WebView socket for PID {pid}")
    socket_name = f"webview_devtools_remote_{pid}"
    
    # Check if the socket exists
    output = run_cmd(logger, "adb", "-s", serial, "shell", "grep", "-a", socket_name, "/proc/net/unix")
    if output and socket_name in output:
        logger.info(f"Found WebView socket: {socket_name}")
        return socket_name
    
    logger.warning(f"WebView socket not found for PID {pid}")
    return None

def forward_port(logger, serial: str, socket_name: str, local_port: int = 9222) -> bool:
    """
    Forward local port to WebView socket
    
    Args:
        logger: Logger instance
        serial: Device serial in format ip:port
        socket_name: WebView socket name
        local_port: Local port to forward to (default: 9222)
        
    Returns:
        True if successful, False otherwise
    """
    logger.info(f"Forwarding local port {local_port} to {socket_name}")
    result = run_cmd(logger, "adb", "-s", serial, "forward", f"tcp:{local_port}", f"localabstract:{socket_name}")
    if result is None:
        logger.error("Port forwarding failed")
        return False
    
    logger.info(f"Port {local_port} forwarded successfully")
    return True

def get_cdp_targets(logger, cdp_url: str) -> Optional[list]:
    """
    Get CDP debug targets
    
    Args:
        logger: Logger instance
        local_port: Local port where WebView is forwarded (default: 9222)
        cdp_url: CDP URL (default: http://localhost:9222)
        
    Returns:
        List of debug targets or None if failed
    """
    logger.info(f"Fetching debug targets from {cdp_url}")
    
    try:
        response = requests.get(cdp_url, timeout=5)
        if response.status_code == 200:
            targets = response.json()
            logger.info(f"Successfully fetched {len(targets)} debug targets")
            return targets
        else:
            logger.error(f"Failed to fetch debug targets. Status code: {response.status_code}")
            return None
    except Exception as e:
        logger.error("Error connecting to debugger")
        logger.exception(e)
        return None

def get_cdp_connection(device_serial: str, process_name: str = "org.chromium.webview_shell", 
                      local_port: int = 9222) -> Optional[Dict[str, Any]]:
    """
    Get CDP connection for a specific device and process
    
    Args:
        device_serial: Device serial in format ip:port
        process_name: Process name to connect to (default: org.chromium.webview_shell)
        local_port: Local port to use for connection (default: 9222)
        
    Returns:
        Dictionary with connection info or None if failed
    """
    logger = setup_logger()
    logger.info(f"=== Getting CDP connection for {process_name} on {device_serial} ===")
    
    # Get process PID
    pid = get_process_pid(logger, device_serial, process_name)
    if not pid:
        logger.error(f"Could not find PID for process {process_name}")
        return None
    
    # Get WebView socket
    socket_name = get_webview_socket(logger, device_serial, pid)
    if not socket_name:
        logger.error(f"Could not find WebView socket for PID {pid}")
        return None
    
    # Forward port
    if not forward_port(logger, device_serial, socket_name, local_port):
        logger.error("Failed to forward port")
        return None
    
    # Get debug targets
    cdp_url = f"http://localhost:{local_port}/json/list"

    targets = get_cdp_targets(logger, cdp_url)
    if not targets:
        logger.error("No debug targets found")
        return None
    
    # Return connection information
    connection_info = {
        "device_serial": device_serial,
        "process_name": process_name,
        "pid": pid,
        "socket_name": socket_name,
        "local_port": local_port,
        "targets": targets
    }
    
    logger.info("=== CDP connection established successfully ===")
    return connection_info

# Example usage
if __name__ == "__main__":
    # Example: Connect to org.chromium.webview_shell on device ***********:4631
    connection = get_cdp_connection("***********:4631", "org.chromium.webview_shell", 9921)
    if connection:
        print("CDP Connection established:")
        print(f"  Device: {connection['device_serial']}")
        print(f"  Process: {connection['process_name']}")
        print(f"  PID: {connection['pid']}")
        print(f"  Socket: {connection['socket_name']}")
        print(f"  Local Port: {connection['local_port']}")
        print(f"  Targets Found: {len(connection['targets'])}")
        
        for i, target in enumerate(connection['targets'], 1):
            print(f"  Target {i}:")
            print(f"    Title: {target.get('title', 'N/A')}")
            print(f"    URL: {target.get('url', 'N/A')}")
            print(f"    WebSocket URL: {target.get('webSocketDebuggerUrl', 'N/A')}")
    else:
        print("Failed to establish CDP connection")