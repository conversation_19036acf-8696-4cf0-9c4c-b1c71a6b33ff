#!/usr/bin/env python3
"""
日志管理工具
用于查看、分析和管理Browser Use API的日志文件
"""

import os
import argparse
import glob
import gzip
import shutil
from datetime import datetime, timedelta
from collections import defaultdict
import re


class LogManager:
    """日志管理器"""
    
    def __init__(self, log_dir="logs"):
        self.log_dir = log_dir
        
    def list_log_files(self):
        """列出所有日志文件"""
        if not os.path.exists(self.log_dir):
            print(f"日志目录 {self.log_dir} 不存在")
            return
        
        print(f"\n=== 日志文件列表 ({self.log_dir}) ===")
        
        # 获取所有日志文件
        log_files = glob.glob(os.path.join(self.log_dir, "*.log*"))
        log_files.sort()
        
        total_size = 0
        for log_file in log_files:
            size = os.path.getsize(log_file)
            total_size += size
            size_mb = size / (1024 * 1024)
            mtime = datetime.fromtimestamp(os.path.getmtime(log_file))
            
            print(f"  {os.path.basename(log_file):<30} {size_mb:>8.2f}MB  {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
        
        print(f"\n总大小: {total_size / (1024 * 1024):.2f}MB")
        print(f"文件数量: {len(log_files)}")
    
    def tail_log(self, log_type="main", lines=50):
        """查看日志文件末尾"""
        log_files = {
            "main": "browser_use_api.log",
            "error": "error.log",
            "request": "requests.log",
            "business": "business.log"
        }
        
        if log_type not in log_files:
            print(f"未知的日志类型: {log_type}")
            print(f"可用类型: {', '.join(log_files.keys())}")
            return
        
        log_file = os.path.join(self.log_dir, log_files[log_type])
        
        if not os.path.exists(log_file):
            print(f"日志文件不存在: {log_file}")
            return
        
        print(f"\n=== {log_files[log_type]} 最后 {lines} 行 ===")
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                tail_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                
                for line in tail_lines:
                    print(line.rstrip())
        except Exception as e:
            print(f"读取日志文件失败: {e}")
    
    def search_logs(self, pattern, log_type="main", case_sensitive=False):
        """搜索日志内容"""
        log_files = {
            "main": "browser_use_api.log",
            "error": "error.log", 
            "request": "requests.log",
            "business": "business.log",
            "all": "*.log"
        }
        
        if log_type not in log_files:
            print(f"未知的日志类型: {log_type}")
            return
        
        flags = 0 if case_sensitive else re.IGNORECASE
        regex = re.compile(pattern, flags)
        
        if log_type == "all":
            search_files = glob.glob(os.path.join(self.log_dir, "*.log"))
        else:
            search_files = [os.path.join(self.log_dir, log_files[log_type])]
        
        print(f"\n=== 搜索结果: '{pattern}' ===")
        
        total_matches = 0
        for log_file in search_files:
            if not os.path.exists(log_file):
                continue
                
            file_matches = 0
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    for line_num, line in enumerate(f, 1):
                        if regex.search(line):
                            if file_matches == 0:
                                print(f"\n--- {os.path.basename(log_file)} ---")
                            print(f"{line_num:>6}: {line.rstrip()}")
                            file_matches += 1
                            total_matches += 1
            except Exception as e:
                print(f"搜索文件 {log_file} 失败: {e}")
        
        print(f"\n总共找到 {total_matches} 个匹配项")
    
    def analyze_logs(self, days=7):
        """分析日志统计信息"""
        print(f"\n=== 最近 {days} 天日志分析 ===")
        
        # 统计信息
        stats = {
            'total_lines': 0,
            'error_count': 0,
            'warning_count': 0,
            'request_count': 0,
            'task_count': 0,
            'daily_stats': defaultdict(lambda: {'requests': 0, 'errors': 0, 'tasks': 0})
        }
        
        # 分析主日志文件
        main_log = os.path.join(self.log_dir, "browser_use_api.log")
        if os.path.exists(main_log):
            try:
                with open(main_log, 'r', encoding='utf-8') as f:
                    for line in f:
                        stats['total_lines'] += 1
                        
                        # 提取日期
                        date_match = re.match(r'(\d{4}-\d{2}-\d{2})', line)
                        if date_match:
                            date_str = date_match.group(1)
                            
                            # 统计不同类型的日志
                            if 'ERROR' in line:
                                stats['error_count'] += 1
                                stats['daily_stats'][date_str]['errors'] += 1
                            elif 'WARNING' in line:
                                stats['warning_count'] += 1
                            elif 'POST /run-task' in line:
                                stats['request_count'] += 1
                                stats['daily_stats'][date_str]['requests'] += 1
                            elif 'Task' in line and 'completed' in line:
                                stats['task_count'] += 1
                                stats['daily_stats'][date_str]['tasks'] += 1
            except Exception as e:
                print(f"分析日志失败: {e}")
                return
        
        # 输出统计结果
        print(f"总日志行数: {stats['total_lines']:,}")
        print(f"错误数量: {stats['error_count']:,}")
        print(f"警告数量: {stats['warning_count']:,}")
        print(f"请求数量: {stats['request_count']:,}")
        print(f"完成任务数: {stats['task_count']:,}")
        
        # 每日统计
        if stats['daily_stats']:
            print(f"\n每日统计:")
            print(f"{'日期':<12} {'请求':<8} {'错误':<8} {'任务':<8}")
            print("-" * 40)
            
            for date_str in sorted(stats['daily_stats'].keys())[-days:]:
                daily = stats['daily_stats'][date_str]
                print(f"{date_str:<12} {daily['requests']:<8} {daily['errors']:<8} {daily['tasks']:<8}")
    
    def clean_old_logs(self, days=30, dry_run=True):
        """清理旧日志文件"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        print(f"\n=== 清理 {days} 天前的日志文件 ===")
        print(f"截止日期: {cutoff_date.strftime('%Y-%m-%d')}")
        
        if dry_run:
            print("(预览模式，不会实际删除文件)")
        
        log_files = glob.glob(os.path.join(self.log_dir, "*.log.*"))
        deleted_count = 0
        deleted_size = 0
        
        for log_file in log_files:
            mtime = datetime.fromtimestamp(os.path.getmtime(log_file))
            if mtime < cutoff_date:
                size = os.path.getsize(log_file)
                deleted_size += size
                deleted_count += 1
                
                print(f"  删除: {os.path.basename(log_file)} ({size/1024/1024:.2f}MB)")
                
                if not dry_run:
                    try:
                        os.remove(log_file)
                    except Exception as e:
                        print(f"    删除失败: {e}")
        
        print(f"\n{'预计' if dry_run else '实际'}删除 {deleted_count} 个文件，释放 {deleted_size/1024/1024:.2f}MB 空间")
        
        if dry_run and deleted_count > 0:
            print("使用 --no-dry-run 参数实际执行删除操作")
    
    def compress_logs(self, days=7):
        """压缩旧日志文件"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        print(f"\n=== 压缩 {days} 天前的日志文件 ===")
        
        log_files = glob.glob(os.path.join(self.log_dir, "*.log.*"))
        compressed_count = 0
        saved_size = 0
        
        for log_file in log_files:
            if log_file.endswith('.gz'):
                continue
                
            mtime = datetime.fromtimestamp(os.path.getmtime(log_file))
            if mtime < cutoff_date:
                original_size = os.path.getsize(log_file)
                compressed_file = log_file + '.gz'
                
                try:
                    with open(log_file, 'rb') as f_in:
                        with gzip.open(compressed_file, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    
                    compressed_size = os.path.getsize(compressed_file)
                    saved_size += (original_size - compressed_size)
                    compressed_count += 1
                    
                    print(f"  压缩: {os.path.basename(log_file)} "
                          f"({original_size/1024/1024:.2f}MB -> {compressed_size/1024/1024:.2f}MB)")
                    
                    # 删除原文件
                    os.remove(log_file)
                    
                except Exception as e:
                    print(f"  压缩失败 {log_file}: {e}")
        
        print(f"\n压缩了 {compressed_count} 个文件，节省 {saved_size/1024/1024:.2f}MB 空间")


def main():
    parser = argparse.ArgumentParser(description="Browser Use API 日志管理工具")
    parser.add_argument("--log-dir", default="logs", help="日志目录路径")
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 列出日志文件
    subparsers.add_parser("list", help="列出所有日志文件")
    
    # 查看日志末尾
    tail_parser = subparsers.add_parser("tail", help="查看日志文件末尾")
    tail_parser.add_argument("--type", default="main", 
                           choices=["main", "error", "request", "business"],
                           help="日志类型")
    tail_parser.add_argument("--lines", type=int, default=50, help="显示行数")
    
    # 搜索日志
    search_parser = subparsers.add_parser("search", help="搜索日志内容")
    search_parser.add_argument("pattern", help="搜索模式（正则表达式）")
    search_parser.add_argument("--type", default="main",
                             choices=["main", "error", "request", "business", "all"],
                             help="日志类型")
    search_parser.add_argument("--case-sensitive", action="store_true", help="区分大小写")
    
    # 分析日志
    analyze_parser = subparsers.add_parser("analyze", help="分析日志统计信息")
    analyze_parser.add_argument("--days", type=int, default=7, help="分析天数")
    
    # 清理日志
    clean_parser = subparsers.add_parser("clean", help="清理旧日志文件")
    clean_parser.add_argument("--days", type=int, default=30, help="保留天数")
    clean_parser.add_argument("--no-dry-run", action="store_true", help="实际执行删除")
    
    # 压缩日志
    compress_parser = subparsers.add_parser("compress", help="压缩旧日志文件")
    compress_parser.add_argument("--days", type=int, default=7, help="压缩天数前的文件")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    log_manager = LogManager(args.log_dir)
    
    if args.command == "list":
        log_manager.list_log_files()
    elif args.command == "tail":
        log_manager.tail_log(args.type, args.lines)
    elif args.command == "search":
        log_manager.search_logs(args.pattern, args.type, args.case_sensitive)
    elif args.command == "analyze":
        log_manager.analyze_logs(args.days)
    elif args.command == "clean":
        log_manager.clean_old_logs(args.days, not args.no_dry_run)
    elif args.command == "compress":
        log_manager.compress_logs(args.days)


if __name__ == "__main__":
    main()
