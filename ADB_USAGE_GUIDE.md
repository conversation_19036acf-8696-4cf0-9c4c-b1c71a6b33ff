# ADB设备连接使用指南

## 📱 概述

重构后的Browser Use API现在支持通过ADB连接到Android设备上的WebView进行自动化操作。这个功能允许您直接控制真实的Android设备，而不仅仅是桌面浏览器。

## 🔧 前置条件

### 1. ADB工具安装
确保您的系统已安装ADB工具：

```bash
# Windows (使用Chocolatey)
choco install adb

# macOS (使用Homebrew)
brew install android-platform-tools

# Ubuntu/Debian
sudo apt-get install android-tools-adb
```

### 2. Android设备准备
1. 在Android设备上启用开发者选项
2. 启用USB调试
3. 如果使用网络ADB，启用网络调试

### 3. 设备连接
```bash
# USB连接
adb devices

# 网络连接
adb connect <device_ip>:<port>
# 例如: adb connect ***********:4631
```

## 🚀 快速开始

### 1. 测试ADB连接
使用提供的测试脚本验证ADB连接：

```bash
python test_adb_connection.py ***********:4631
```

成功输出示例：
```
=== ADB连接测试 ===
设备序列号: ***********:4631
进程名称: org.chromium.webview_shell
本地端口: 9222

正在建立ADB连接...
✅ ADB连接建立成功!

连接信息:
  设备序列号: ***********:4631
  进程名称: org.chromium.webview_shell
  进程PID: 12345
  WebView Socket: webview_devtools_remote_12345
  本地端口: 9222
  调试目标数量: 2

CDP连接URL: http://localhost:9222
✅ 测试完成，连接正常!
```

### 2. 启动API服务
```bash
python main.py
```

### 3. 创建ADB任务
使用API创建连接到ADB设备的任务：

```bash
# 使用测试脚本
python test_api_with_adb.py create "打开淘宝并搜索商品" ***********:4631

# 或直接使用curl
curl -X POST http://localhost:8080/api/aia/auto/run-task \
  -H "Content-Type: application/json" \
  -d '{
    "v": "1.0.0",
    "arg": {
      "taskId": "adb-test-001",
      "task": "打开淘宝并搜索iPhone",
      "extSysMsg": "请仔细操作，确保点击正确的元素",
      "adbDevice": "***********:4631"
    }
  }'
```

## 📋 API使用说明

### 请求格式
```json
{
  "v": "1.0.0",
  "arg": {
    "taskId": "可选的任务ID",
    "task": "任务描述",
    "extSysMsg": "扩展系统消息",
    "adbDevice": "设备序列号，格式为ip:port"
  }
}
```

### 参数说明
- `taskId`: 可选，任务唯一标识符，留空则自动生成
- `task`: 必需，任务描述，告诉AI要执行什么操作
- `extSysMsg`: 可选，给AI的额外指令或上下文信息
- `adbDevice`: 可选，ADB设备序列号，留空则使用默认CDP连接

### 响应格式
```json
{
  "code": 0,
  "msg": "Task created and started",
  "data": {
    "taskId": "generated-task-id",
    "status": "pending",
    "message": "Task created and started"
  }
}
```

## ⚙️ 配置选项

### 环境变量配置
在 `.env` 文件中配置ADB相关选项：

```bash
# ADB端口转发的本地端口
ADB_LOCAL_PORT=9222

# 目标进程名称
ADB_PROCESS_NAME=org.chromium.webview_shell
```

### 支持的进程名称
常见的WebView进程名称：
- `org.chromium.webview_shell` (默认)
- `com.android.chrome`
- `com.tencent.mm` (微信)
- `com.taobao.taobao` (淘宝)
- 其他包含WebView的应用

## 🔍 故障排除

### 1. 连接失败
```bash
# 检查设备连接
adb devices

# 检查进程是否存在
adb -s ***********:4631 shell ps -A | grep webview

# 检查端口是否被占用
netstat -an | grep 9222
```

### 2. 进程未找到
如果找不到目标进程，可能需要：
1. 在设备上打开包含WebView的应用
2. 确保应用启用了WebView调试
3. 检查进程名称是否正确

### 3. 端口冲突
如果本地端口被占用：
1. 修改 `ADB_LOCAL_PORT` 环境变量
2. 或者杀死占用端口的进程

### 4. 权限问题
确保ADB有足够权限：
```bash
# 重启ADB服务
adb kill-server
adb start-server

# 检查设备授权
adb devices
```

## 📊 监控和调试

### 1. 查看任务状态
```bash
# 获取特定任务状态
curl http://localhost:8080/api/aia/auto/tasks/{taskId}

# 获取所有任务列表
curl http://localhost:8080/api/aia/auto/tasks
```

### 2. 日志监控
查看详细的执行日志：
```bash
# 查看主日志
tail -f logs/browser_use_api.log

# 查看业务日志
tail -f logs/browser_use_api.business.log

# 查看请求日志
tail -f logs/browser_use_api.requests.log
```

### 3. 健康检查
```bash
curl http://localhost:8080/health
```

## 🎯 最佳实践

### 1. 任务描述编写
- 使用清晰、具体的描述
- 包含必要的上下文信息
- 避免过于复杂的多步骤操作

### 2. 设备管理
- 确保设备屏幕保持唤醒
- 避免在任务执行期间手动操作设备
- 定期检查设备连接状态

### 3. 错误处理
- 设置合理的超时时间
- 实现任务重试机制
- 监控任务执行状态

## 🔗 相关文件

- `adb_utils/webview_forward.py` - ADB连接核心逻辑
- `config.py` - 配置管理
- `task_manager.py` - 任务执行管理
- `test_adb_connection.py` - ADB连接测试工具
- `test_api_with_adb.py` - API测试工具

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件获取详细错误信息
2. 使用测试脚本验证各个组件
3. 检查网络连接和设备状态
4. 参考故障排除部分的解决方案
