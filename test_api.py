#!/usr/bin/env python3
"""
测试优化后的Browser Use API
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:8080"

def test_health_check():
    """测试健康检查端点"""
    print("=== 测试健康检查端点 ===")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_root_endpoint():
    """测试根端点"""
    print("\n=== 测试根端点 ===")
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"根端点测试失败: {e}")
        return False

def test_create_task():
    """测试创建任务"""
    print("\n=== 测试创建任务 ===")
    try:
        task_data = {
            "task": "测试任务：访问百度首页",
            "contextMsg": "这是一个测试任务"
        }
        
        response = requests.post(f"{BASE_URL}/run-task", json=task_data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            return response.json().get("taskId")
        return None
    except Exception as e:
        print(f"创建任务失败: {e}")
        return None

def test_get_task(task_id):
    """测试查询任务状态"""
    print(f"\n=== 测试查询任务状态 (ID: {task_id}) ===")
    try:
        response = requests.get(f"{BASE_URL}/tasks/{task_id}")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"查询任务失败: {e}")
        return False

def test_list_tasks():
    """测试获取任务列表"""
    print("\n=== 测试获取任务列表 ===")
    try:
        response = requests.get(f"{BASE_URL}/tasks")
        print(f"状态码: {response.status_code}")
        tasks = response.json()
        print(f"任务数量: {len(tasks)}")
        for task in tasks:
            print(f"  - 任务ID: {task['taskId']}, 状态: {task['status']}")
        return response.status_code == 200
    except Exception as e:
        print(f"获取任务列表失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试优化后的Browser Use API...")
    
    # 测试健康检查
    if not test_health_check():
        print("健康检查失败，请确保API服务正在运行")
        return
    
    # 测试根端点
    test_root_endpoint()
    
    # 测试创建任务
    task_id = test_create_task()
    if task_id:
        # 等待一下，然后查询任务状态
        time.sleep(2)
        test_get_task(task_id)
    
    # 测试获取任务列表
    test_list_tasks()
    
    print("\n=== 测试完成 ===")
    print("请检查以下日志文件查看详细的日志记录:")
    print("  - logs/browser_use_api.log  (主日志)")
    print("  - logs/requests.log         (请求日志)")
    print("  - logs/business.log         (业务日志)")
    print("  - logs/error.log            (错误日志)")
    print("\n使用日志管理工具查看日志:")
    print("  python log_manager.py list                    # 列出所有日志文件")
    print("  python log_manager.py tail --type main        # 查看主日志末尾")
    print("  python log_manager.py search 'ERROR'          # 搜索错误日志")
    print("  python log_manager.py analyze                 # 分析日志统计")

if __name__ == "__main__":
    main()
