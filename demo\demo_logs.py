#!/usr/bin/env python3
"""
日志系统演示脚本
展示高级日志系统的各种功能
"""

import time
import random
from log_config import setup_logging, get_logger


def demo_basic_logging():
    """演示基本日志功能"""
    print("\n=== 演示基本日志功能 ===")
    
    logger = get_logger("demo")
    
    logger.debug("这是一条调试信息")
    logger.info("这是一条普通信息")
    logger.warning("这是一条警告信息")
    logger.error("这是一条错误信息")
    logger.critical("这是一条严重错误信息")
    
    print("✅ 基本日志演示完成")


def demo_structured_logging():
    """演示结构化日志"""
    print("\n=== 演示结构化日志 ===")
    
    logger = get_logger("demo.structured")
    request_logger = get_logger("browser_use_api.requests")
    business_logger = get_logger("browser_use_api.business")
    
    # 模拟HTTP请求日志
    request_logger.info("Request started - Method: POST | URL: /run-task | Client IP: 127.0.0.1")
    time.sleep(0.1)
    request_logger.info("Request completed - Method: POST | URL: /run-task | Status: 200 | Process time: 0.123s")
    
    # 模拟业务逻辑日志
    task_id = "demo-12345"
    business_logger.info(f"Task {task_id} - Starting browser task execution")
    business_logger.info(f"Task {task_id} - Configuring LLM (DeepSeek)")
    business_logger.info(f"Task {task_id} - Browser session configured")
    time.sleep(0.2)
    business_logger.info(f"Task {task_id} - Task completed successfully")
    
    print("✅ 结构化日志演示完成")


def demo_error_scenarios():
    """演示错误场景日志"""
    print("\n=== 演示错误场景日志 ===")
    
    logger = get_logger("demo.errors")
    business_logger = get_logger("browser_use_api.business")
    
    # 模拟各种错误场景
    scenarios = [
        ("连接超时", "Connection timeout after 30 seconds"),
        ("API密钥无效", "Invalid API key provided"),
        ("浏览器启动失败", "Failed to start browser session"),
        ("任务执行异常", "Task execution failed with exception: ValueError"),
        ("内存不足", "Out of memory error during task execution")
    ]
    
    for scenario, error_msg in scenarios:
        task_id = f"error-{random.randint(1000, 9999)}"
        business_logger.error(f"Task {task_id} - {scenario}: {error_msg}")
        time.sleep(0.1)
    
    print("✅ 错误场景演示完成")


def demo_performance_logging():
    """演示性能日志"""
    print("\n=== 演示性能日志 ===")
    
    logger = get_logger("demo.performance")
    request_logger = get_logger("browser_use_api.requests")
    
    # 模拟不同性能的请求
    performance_cases = [
        ("快速请求", 0.1),
        ("正常请求", 0.5),
        ("慢请求", 2.1),
        ("超慢请求", 5.3),
        ("极慢请求", 10.7)
    ]
    
    for case_name, duration in performance_cases:
        start_time = time.time()
        request_logger.info(f"Request started - {case_name}")
        time.sleep(min(duration, 0.2))  # 实际演示时缩短等待时间
        actual_duration = time.time() - start_time
        request_logger.info(f"Request completed - {case_name} | Process time: {duration:.3f}s")
        
        if duration > 5.0:
            logger.warning(f"Slow request detected: {case_name} took {duration:.3f}s")
    
    print("✅ 性能日志演示完成")


def demo_task_lifecycle():
    """演示完整任务生命周期日志"""
    print("\n=== 演示任务生命周期日志 ===")
    
    logger = get_logger("demo.lifecycle")
    business_logger = get_logger("browser_use_api.business")
    
    task_id = f"lifecycle-{random.randint(10000, 99999)}"
    
    # 任务创建
    logger.info(f"POST /run-task - Creating new browser task")
    logger.info(f"Generated new taskId: {task_id}")
    logger.info(f"Task {task_id} - Creating task with description: 访问百度首页并搜索")
    
    # 任务执行开始
    business_logger.info(f"Task {task_id} - Starting browser task execution")
    business_logger.info(f"Task {task_id} - Status updated to RUNNING")
    business_logger.info(f"Task {task_id} - Configuring LLM (DeepSeek)")
    time.sleep(0.1)
    
    # 浏览器配置
    business_logger.info(f"Task {task_id} - LLM configuration completed")
    business_logger.info(f"Task {task_id} - Configuring browser session")
    business_logger.info(f"Task {task_id} - Browser session configured with mobile profile")
    time.sleep(0.1)
    
    # Agent创建和执行
    business_logger.info(f"Task {task_id} - Creating browser agent")
    business_logger.info(f"Task {task_id} - Agent created successfully")
    business_logger.info(f"Task {task_id} - Starting agent execution")
    time.sleep(0.2)
    
    # 任务完成
    execution_time = 3.456
    business_logger.info(f"Task {task_id} - Agent execution completed in {execution_time:.3f}s")
    business_logger.info(f"Task {task_id} - Task completed successfully")
    business_logger.info(f"Task {task_id} - Final result: 成功访问百度首页并完成搜索")
    business_logger.info(f"Task {task_id} - Cleaned up from running tasks")
    
    print("✅ 任务生命周期演示完成")


def demo_concurrent_tasks():
    """演示并发任务日志"""
    print("\n=== 演示并发任务日志 ===")
    
    business_logger = get_logger("browser_use_api.business")
    
    # 模拟3个并发任务
    task_ids = [f"concurrent-{i}" for i in range(1, 4)]
    
    # 所有任务开始
    for task_id in task_ids:
        business_logger.info(f"Task {task_id} - Starting browser task execution")
        time.sleep(0.05)
    
    # 交错执行日志
    for i in range(5):
        for task_id in task_ids:
            stage = ["Configuring LLM", "Setting up browser", "Creating agent", "Executing task", "Completing task"][i]
            business_logger.info(f"Task {task_id} - {stage}")
            time.sleep(0.02)
    
    # 任务完成（不同时间）
    for i, task_id in enumerate(task_ids):
        execution_time = 2.1 + i * 0.5
        business_logger.info(f"Task {task_id} - Task completed successfully in {execution_time:.3f}s")
        time.sleep(0.1)
    
    print("✅ 并发任务演示完成")


def main():
    """主演示函数"""
    print("🚀 Browser Use API 高级日志系统演示")
    print("=" * 50)
    
    # 设置日志系统
    setup_logging(
        log_dir="demo_logs",
        log_level="INFO",
        backup_count=7,
        enable_console_colors=True
    )
    
    logger = get_logger("demo")
    logger.info("日志系统演示开始")
    
    try:
        # 运行各种演示
        demo_basic_logging()
        demo_structured_logging()
        demo_error_scenarios()
        demo_performance_logging()
        demo_task_lifecycle()
        demo_concurrent_tasks()
        
        print("\n" + "=" * 50)
        print("🎉 所有演示完成！")
        print("\n📁 演示日志文件位置:")
        print("  - demo_logs/browser_use_api.log  (主日志)")
        print("  - demo_logs/requests.log         (请求日志)")
        print("  - demo_logs/business.log         (业务日志)")
        print("  - demo_logs/error.log            (错误日志)")
        
        print("\n🔍 查看演示日志:")
        print("  python log_manager.py --log-dir demo_logs list")
        print("  python log_manager.py --log-dir demo_logs tail --type main")
        print("  python log_manager.py --log-dir demo_logs search 'ERROR'")
        print("  python log_manager.py --log-dir demo_logs analyze")
        
        logger.info("日志系统演示完成")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        print(f"❌ 演示失败: {e}")


if __name__ == "__main__":
    main()
