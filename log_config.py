"""
日志配置模块
支持按天滚动、分级日志、结构化输出等功能
"""

import os
import logging
import logging.config
from datetime import datetime


class ColoredFormatter(logging.Formatter):
    """带颜色的控制台日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


def setup_logging(
    log_dir: str = "logs",
    log_level: str = "INFO",
    backup_count: int = 30,
    enable_console_colors: bool = True
):
    """
    设置日志配置
    
    Args:
        log_dir: 日志目录
        log_level: 日志级别
        max_bytes: 单个日志文件最大大小
        backup_count: 保留的日志文件数量
        enable_console_colors: 是否启用控制台颜色
    """
    
    # 创建日志目录
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 日志格式
    detailed_format = '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
    simple_format = '%(asctime)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # 配置日志记录器
    logging_config = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'detailed': {
                'format': detailed_format,
                'datefmt': date_format
            },
            'simple': {
                'format': simple_format,
                'datefmt': date_format
            },
            'colored': {
                '()': ColoredFormatter,
                'format': simple_format,
                'datefmt': date_format
            } if enable_console_colors else {
                'format': simple_format,
                'datefmt': date_format
            }
        },
        'handlers': {
            'main_file': {
                'class': 'logging.handlers.TimedRotatingFileHandler',
                'filename': os.path.join(log_dir, 'browser_use_api.log'),
                'when': 'midnight',
                'interval': 1,
                'backupCount': backup_count,
                'encoding': 'utf-8',
                'formatter': 'detailed'
            },
            'error_file': {
                'class': 'logging.handlers.TimedRotatingFileHandler',
                'filename': os.path.join(log_dir, 'error.log'),
                'when': 'midnight',
                'interval': 1,
                'backupCount': backup_count,
                'encoding': 'utf-8',
                'level': 'ERROR',
                'formatter': 'detailed'
            },
            'request_file': {
                'class': 'logging.handlers.TimedRotatingFileHandler',
                'filename': os.path.join(log_dir, 'requests.log'),
                'when': 'midnight',
                'interval': 1,
                'backupCount': backup_count,
                'encoding': 'utf-8',
                'formatter': 'simple'
            },
            'business_file': {
                'class': 'logging.handlers.TimedRotatingFileHandler',
                'filename': os.path.join(log_dir, 'business.log'),
                'when': 'midnight',
                'interval': 1,
                'backupCount': backup_count,
                'encoding': 'utf-8',
                'formatter': 'simple'
            },
            'console': {
                'class': 'logging.StreamHandler',
                'formatter': 'colored' if enable_console_colors else 'simple'
            }
        },
        'loggers': {
            'browser_use_api': {
                'level': log_level,
                'handlers': ['main_file', 'error_file', 'console'],
                'propagate': False
            },
            'browser_use_api.requests': {
                'level': log_level,
                'handlers': ['request_file', 'console'],
                'propagate': False
            },
            'browser_use_api.business': {
                'level': log_level,
                'handlers': ['business_file', 'console'],
                'propagate': False
            }
        },
        'root': {
            'level': log_level,
            'handlers': ['main_file', 'console']
        }
    }
    
    # 应用配置
    logging.config.dictConfig(logging_config)
    
    # 记录日志系统启动信息
    logger = logging.getLogger("browser_use_api")
    logger.info("="*60)
    logger.info("Browser Use API 高级日志系统启动")
    logger.info(f"日志目录: {os.path.abspath(log_dir)}")
    logger.info(f"日志级别: {log_level}")
    logger.info(f"日志文件:")
    logger.info(f"  - 主日志: browser_use_api.log")
    logger.info(f"  - 错误日志: error.log")
    logger.info(f"  - 请求日志: requests.log")
    logger.info(f"  - 业务日志: business.log")
    logger.info(f"日志滚动: 每天午夜，保留{backup_count}天")
    logger.info(f"控制台颜色: {'启用' if enable_console_colors else '禁用'}")
    logger.info("="*60)
    
    return logger


def get_logger(name: str = "browser_use_api"):
    """获取日志记录器"""
    return logging.getLogger(name)


def log_system_info():
    """记录系统信息"""
    logger = get_logger()
    logger.info(f"系统启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"Python版本: {os.sys.version}")
    logger.info(f"工作目录: {os.getcwd()}")


if __name__ == "__main__":
    # 测试日志配置
    setup_logging()
    log_system_info()
    
    logger = get_logger()
    request_logger = get_logger("browser_use_api.requests")
    business_logger = get_logger("browser_use_api.business")
    
    # 测试不同级别的日志
    logger.debug("这是一个调试消息")
    logger.info("这是一个信息消息")
    logger.warning("这是一个警告消息")
    logger.error("这是一个错误消息")
    
    request_logger.info("测试请求日志")
    business_logger.info("测试业务日志")
    
    print("日志测试完成，请检查 logs/ 目录下的日志文件")
